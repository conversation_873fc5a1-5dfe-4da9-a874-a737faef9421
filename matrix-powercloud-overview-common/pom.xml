<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.electric</groupId>
        <artifactId>matrix-powercloud-overview-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>matrix-powercloud-overview-common</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cet.powercloud</groupId>
            <artifactId>wisdom-feign-spring-boot-starter</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>matrix-powercloud-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.cet.electric</groupId>
                    <artifactId>cet-commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.cet.electric</groupId>
                    <artifactId>fusion-matrix-v2-modelsdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>fusion-matrix-v2-modelsdk</artifactId>
            <version>1.0.13-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.7</version>
        </dependency>

    </dependencies>
</project>
