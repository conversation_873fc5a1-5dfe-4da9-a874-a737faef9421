package com.cet.electric.powercloud.overview.common.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新能源总览打点返回值
 *
 * <AUTHOR>
 * @date 2024/6/17
 */
@Data
public class PvOverviewRoomInfoResponse {
    @ApiModelProperty("站点id")
    private Long roomId;
    @ApiModelProperty("站点类型")
    private Integer roomType;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("纬度")
    private Double latitude;
    @ApiModelProperty("经度")
    private Double longitude;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("装机容量")
    private Double capacity;

    @ApiModelProperty("实时功率")
    private Double currentPower;
    @ApiModelProperty("今日发电量")
    private Double dailyPowerOutput;
    @ApiModelProperty("累计发电量")
    private Double totalPowerOutput;

    @ApiModelProperty("并网电压(kV)")
    private Double girdVoltage;
    @ApiModelProperty("装机功率(kW)")
    private Double totalRatedPower;
    @ApiModelProperty("等效利用系数")
    private Double value;

    private int onlineCount;
    private int offlineCount;
    private Long eventCount;

    @ApiModelProperty("总充电量")
    private Double chargingAmount;
    @ApiModelProperty("总放电量")
    private Double dischargingAmount;
}
