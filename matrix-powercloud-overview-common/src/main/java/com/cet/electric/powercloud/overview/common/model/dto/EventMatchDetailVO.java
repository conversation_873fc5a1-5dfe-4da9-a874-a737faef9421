package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 事件详情参数
 * @date 2022/11/7 18:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventMatchDetailVO {

    @ApiModelProperty(value = "事件等级")
    private Integer startEventLevel;
    @ApiModelProperty(value = "事件等级枚举Text")
    private String startEventLevelText;
    @ApiModelProperty(value = "事件类型")
    private Integer eventType;
    @ApiModelProperty(value = "事件类型枚举Text")
    private String eventTypeText;
    @ApiModelProperty(value = "事件状态")
    private Integer confirmEventStatus;
    @ApiModelProperty(value = "事件状态枚举Text")
    private String confirmEventStatusText;
    @ApiModelProperty(value = "事件发生时间")
    private Long startEventTime;
    @ApiModelProperty(value = "描述")
    private String startEventDescription;

    private String deviceLabel;
    private Long deviceId;
    private Long projectId;
    private Long buildingId;
    private Long roomId;

}