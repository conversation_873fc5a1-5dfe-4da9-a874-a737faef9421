package com.cet.electric.powercloud.overview.common.model.request;

import com.cet.electric.powercloud.overview.common.model.dto.PeakVallyParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询峰平谷尖能耗
 * @date 2023/3/1 10:27
 */
@Data
public class QueryMonthPeakVallyDistributeRequest {
    @ApiModelProperty(value = "查询时间段开始时间(时间戳),左闭", example = "1606752000000")
    private Long projectId;
    @ApiModelProperty(value = "查询时间段开始时间(时间戳),左闭", example = "1606752000000")
    private Long startTime;
    @ApiModelProperty(value = "查询时间段结束时间(时间戳),右开", example = "1609430400000")
    private Long endTime;
}
