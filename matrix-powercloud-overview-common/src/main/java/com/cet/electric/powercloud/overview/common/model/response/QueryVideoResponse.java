package com.cet.electric.powercloud.overview.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 配电房摄像头查询结果
 * @date 2022/10/9 11:14
 */
@Data
@ApiModel
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryVideoResponse {
    private Long id;
    @ApiModelProperty(value = "视频链接")
    private String playUrl;
    @ApiModelProperty(value = "名称", example = "分区1")
    private String name;
    @ApiModelProperty(value = "备注", example = "分区1")
    private String remark;
    @ApiModelProperty(value = "appName", example = "K17110504")
    private String appName;
    @ApiModelProperty(value = "streamName", example = "4")
    private String streamName;
    @ApiModelProperty(value = "连接类型", example = "direct")
    private String joinType;
    @ApiModelProperty(value = "原始url", example = "rtsp://ROOT:sA123456@************:554/h264/ch1/main/av_stream")
    private String originUrl;
}
