package com.cet.electric.powercloud.overview.common.enums;

import com.cet.electric.powercloud.common.enums.common.ALanguageEnum;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2024-06-17  16:09
 * @description:
 */
@Getter
@AllArgsConstructor
public enum TransformerCoilTypeEnum {
    DUAL_WINDING(1, "双绕组变压器", 1.33D),
    TRIPLE_WINDING(2, "三绕组变压器", 0.537D);

    private final int type;
    private final String desc;
    private final Double lowerBoundCoefficientOfOptimalEconomic;

    public static TransformerCoilTypeEnum getByType(Integer type) {
        for (TransformerCoilTypeEnum value : TransformerCoilTypeEnum.values()) {
            if (Objects.equals(value.type, type)) {
                return value;
            }
        }
        throw new IllegalArgumentException(LanguageUtil.getMessage(ALanguageEnum.ERROR_ENUM_INVALID.getKey()));
    }
}
