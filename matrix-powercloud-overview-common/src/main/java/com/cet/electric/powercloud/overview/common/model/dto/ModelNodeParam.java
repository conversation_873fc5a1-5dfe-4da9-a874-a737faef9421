package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模型结点参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/6 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "模型节点参数")
public class ModelNodeParam {
    @ApiModelProperty(value = "模型节点Label")
    private String modelLabel;
    @ApiModelProperty(value = "模型节点ID")
    private Long modelId;
}
