package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 全局监测配电房详细信息
 * @date 2022/11/8 14:05
 */
@Data
public class OverviewMapDetailParam {
    @ApiModelProperty("楼栋名称")
    private String buildingName;
    @ApiModelProperty("配电房名称")
    private String roomName;
    @ApiModelProperty(value = "配电室类型")
    private Integer roomType;
    @ApiModelProperty("电压等级")
    private String voltageLevel;
    @ApiModelProperty("变压器台数")
    private Integer transformerNum;
    @ApiModelProperty("负载率")
    private Double loadRate;
    @ApiModelProperty("额定容量")
    private Double ratedCapacity;
    @ApiModelProperty("申报需量")
    private Double declaredDemand;
    @ApiModelProperty("测控装置")
    private Integer measurementDeviceNum;
    @ApiModelProperty("有功功率")
    private Double activePower;
    @ApiModelProperty("无功功率")
    private Double reactivePower;
    @ApiModelProperty("温度")
    private Double temperature;
    @ApiModelProperty("湿度")
    private Double humidity;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("事件信息")
    private EventOverviewParam event;
}
