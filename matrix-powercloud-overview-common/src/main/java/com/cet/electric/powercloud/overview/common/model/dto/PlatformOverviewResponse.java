package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 平台概览返回
 * @date 2022/11/7 14:30
 */
@Data
public class PlatformOverviewResponse {
    @ApiModelProperty("项目总数")
    private Integer projectCount;
    @ApiModelProperty("站点总数")
    private Integer roomCount;
    @ApiModelProperty("安全运行天数")
    private Long safeRunDays;
    @ApiModelProperty("额定总容量")
    private Double ratedTotalCapacity;
//    @ApiModelProperty("当前总功率")
//    private Double currentTotalPower;
//    @ApiModelProperty("碳排放量")
//    private Double carbonEmissions;
    @ApiModelProperty("表计数量")
    private Integer meterCount;
    @ApiModelProperty("变压器数量")
    private Integer transformerCount;

    @ApiModelProperty("在线设备总数量")
    private Integer deviceCount;
    @ApiModelProperty("报警设备总数量")
    private Integer alarmDeviceCount;
    @ApiModelProperty("设备在线率")
    private Double deviceOnlineRate;
    @ApiModelProperty("预警设备占比")
    private Double alarmDevicePercent;

    @ApiModelProperty("本月告警总数")
    private Integer alarmCount;
    @ApiModelProperty("环比上月告警总数")
    private Double chainRatioAlarmCount;

    @ApiModelProperty("已处理告警总数")
    private Integer handledAlarmCount;
    @ApiModelProperty("未处理告警总数")
    private Integer unhandledAlarmCount;
    @ApiModelProperty("处理中告警总数")
    private Integer confirmingNum;
    @ApiModelProperty("查询实时告警事件")
    private List<EventMatchDetailVO> matchDetailVos;

}
