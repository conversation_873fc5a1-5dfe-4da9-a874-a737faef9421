package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 事件排名返回
 * @date 2022/10/27 11:21
 */
@Data
@ApiModel
@NoArgsConstructor
public class EventDistributeParam {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("数量")
    private Long num;
    @ApiModelProperty("站点名称")
    private String roomName;

    public EventDistributeParam(String name, Long num) {
        this.name = name;
        this.num = num;
    }
}
