package com.cet.electric.powercloud.overview.common.model.dto;

import com.cet.electric.powercloud.common.model.PageParams;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 通用查询项目参数
 * @date 2022/9/23 17:26
 */
@Data
@NoArgsConstructor
public class QueryProjectParams {

    private List<Long> ids;
    private String name;

    private String code;
    private PageParams pageParams;

    private Long districtId;

    private Long provinceCode;
    private Long cityCode;
    private Long districtCode;

    public QueryProjectParams(List<Long> ids) {
        this.ids = ids;
    }

    public QueryProjectParams(String name) {
        this.name = name;
    }

    public QueryProjectParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    public QueryProjectParams(Long districtId) {
        this.districtId = districtId;
    }
}
