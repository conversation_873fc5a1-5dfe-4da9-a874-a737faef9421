package com.cet.electric.powercloud.overview.common.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询节点树参数
 * @date 2022/10/31 14:19
 */
@Data
@ApiModel
public class QueryCommonNodeTreeRequest {
    @ApiModelProperty(value = "查询的用户id,不传查自己")
    private Long theUserId;
    @ApiModelProperty(value = "是否根据权限过滤，可不传，默认过滤")
    private Boolean byAuth;
    @ApiModelProperty(value = "节点树根节点")
    private String rootLabel;
    @ApiModelProperty(value = "节点树根节点id,传0查所有")
    private Long rootId;
    @ApiModelProperty(value = "节点树根节点ids")
    private List<Long> rootIds;
    @ApiModelProperty(value = "子层级label")
    private List<String> subLabelList;
    @ApiModelProperty(value = "能源类型，不传无限制")
    private Integer energyType;
    @ApiModelProperty(value = "子层级有配电房时，根据房间类型过滤；")
    private List<Integer> roomTypes;
}
