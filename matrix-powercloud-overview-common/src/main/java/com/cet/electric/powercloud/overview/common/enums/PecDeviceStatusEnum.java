package com.cet.electric.powercloud.overview.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2024-05-10  16:52
 * @description:
 */
@Getter
@AllArgsConstructor
public enum PecDeviceStatusEnum {
    UNKNOWN_OR_DEVICE_DOES_NOT_EXIST(-1, "未知状态或者设备不存在"),
    POLLING_WAITING(0, "轮询等待"),
    COMMUNICATING(1, "正在通信"),
    AUTO_EXIT(2, "自动退出"),
    ATTEMPTING_COMMUNICATION(3, "尝试通信"),
    MANUAL_EXIT(4, "手动退出"),
    MAINTENANCE_SHUTDOWN(5, "检修停运");

    private final int code;
    private final String desc;

    public static boolean isNormal(Integer code) {
        return Objects.equals(code, POLLING_WAITING.getCode()) || Objects.equals(code, COMMUNICATING.getCode());
    }
}
