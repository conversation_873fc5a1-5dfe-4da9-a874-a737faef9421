package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 全局监测地图信息
 * @date 2022/11/8 14:05
 */
@Data
public class OverviewMapParam {
    @ApiModelProperty("站点id")
    private Long id;
    @ApiModelProperty("站点名称")
    private String name;
    @ApiModelProperty("纬度")
    private Double latitude;
    @ApiModelProperty("经度")
    private Double longitude;
    @ApiModelProperty("1是有未处理事件，2是没有未处理但是有处理中，3是没有未处理和处理中")
    private Integer status;
    @ApiModelProperty("地址")
    private String address;
    private Long roomId;
    @ApiModelProperty("站点类型")
    private Integer roomType;
    @ApiModelProperty("装机容量")
    private Double capacity;
    @ApiModelProperty("并网电压(kV)")
    private Double girdVoltage;
    @ApiModelProperty("装机功率(kW)")
    private Double totalRatedPower;


}
