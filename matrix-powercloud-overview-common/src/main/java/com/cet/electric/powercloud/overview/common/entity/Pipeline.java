package com.cet.electric.powercloud.overview.common.entity;


import com.cet.electric.powercloud.common.model.device.DeviceModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: 管道
 * @date 2022/10/9 14:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Pipeline extends DeviceModel {
    @JsonProperty("energytype")
    private Integer energyType;
    @JsonProperty("energytype$text")
    private String energyTypeText;
    @JsonProperty("pipefunctiontype")
    private Integer pipeFunctionType;
    @JsonProperty("pipefunctiontype$text")
    private String pipeFunctionTypeText;
    @JsonProperty("unitsymbol")
    private Integer unitSymbol;
    @JsonProperty("unitsymbol$text")
    private String unitSymbolText;
}
