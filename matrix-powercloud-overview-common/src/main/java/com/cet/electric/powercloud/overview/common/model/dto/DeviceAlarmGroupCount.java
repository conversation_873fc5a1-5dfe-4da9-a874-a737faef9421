package com.cet.electric.powercloud.overview.common.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: joy
 * @Date: 2022/12/20 11:04
 * @Description: 设备报警分组结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceAlarmGroupCount {
    @JsonProperty(value = "count_id")
    private Long count;
    @JsonProperty(value = "object_id")
    private Long deviceId;
    @JsonProperty(value = "object_label")
    private String deviceLabel;
    @JsonProperty(value = "confirmeventstatus")
    private Integer confirmEventStatus;
}
