package com.cet.electric.powercloud.overview.common.model.dto;

import com.cet.electric.powercloud.common.model.PageParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询房间通用参数
 * @date 2022/9/26 10:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryRoomParams {
    private List<Long> ids;
    private String name;

    private String code;

    private Integer roomType;
    private PageParams pageParams;

    private Long projectId;

    private List<Integer> roomTypes;

    public QueryRoomParams(List<Long> ids) {
        this.ids = ids;
    }

    public QueryRoomParams(String name) {
        this.name = name;
    }

    public QueryRoomParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    public QueryRoomParams(Long projectId) {
        this.projectId = projectId;
    }
}
