package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 事件处理及时性
 * @date 2024/2/8 18:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventTimelinessDTO {
    @ApiModelProperty(value = "事件总数")
    private Long eventTotal;
    @ApiModelProperty(value = "未处理事件总数")
    private Long unprocessedEventCount;
    @ApiModelProperty(value = "一小时内处理")
    private Long oneHourProcess;
    @ApiModelProperty(value = "一天内处理")
    private Long oneDayProcess;
    @ApiModelProperty(value = "两天内处理")
    private Long twoDayProcess;
    @ApiModelProperty(value = "一周内处理")
    private Long oneWeekProcess;


    public static EventTimelinessDTO init() {
        return new EventTimelinessDTO(0L, 0L, 0L, 0L, 0L, 0L);
    }
}
