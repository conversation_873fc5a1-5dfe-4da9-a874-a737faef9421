package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 事件统计信息
 * @date 2022/11/7 16:40
 */
@Data
@ApiModel
public class EventOverviewParam {
    @ApiModelProperty("告警总数")
    private Integer alarmCount;
    @ApiModelProperty("已处理告警总数")
    private Integer handledAlarmCount;
    @ApiModelProperty("处理中告警总数")
    private Integer confirmingAlarmCount;
    @ApiModelProperty("未处理告警总数")
    private Integer unhandledAlarmCount;

    public EventOverviewParam initEmpty() {
        this.alarmCount = 0;
        this.handledAlarmCount = 0;
        this.unhandledAlarmCount = 0;
        return this;
    }
}
