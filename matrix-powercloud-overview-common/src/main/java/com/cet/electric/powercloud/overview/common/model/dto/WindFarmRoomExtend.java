package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2023-12-18  13:58
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WindFarmRoomExtend extends BaseRoomExtend {
    @ApiModelProperty("站供电类型")
    private Integer powerSupplyType;
    @ApiModelProperty("发电规模 : 1分布式 ,2集中式")
    private Integer scale;
}
