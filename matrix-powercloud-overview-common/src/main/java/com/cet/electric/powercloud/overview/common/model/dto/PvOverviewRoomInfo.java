package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新能源总览打点返回值
 *
 * <AUTHOR>
 * @date 2023/11/15
 */
@Data
public class PvOverviewRoomInfo {
    @ApiModelProperty("站点id")
    private Long roomId;
    @ApiModelProperty("站点类型")
    private Integer roomType;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("纬度")
    private Double latitude;
    @ApiModelProperty("经度")
    private Double longitude;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("装机容量")
    private Double capacity;
    @ApiModelProperty("并网电压(kV)")
    private Double girdVoltage;
    @ApiModelProperty("装机功率(kW)")
    private Double totalRatedPower;

    @ApiModelProperty("站点是否有告警")
    private Boolean alarm;
}
