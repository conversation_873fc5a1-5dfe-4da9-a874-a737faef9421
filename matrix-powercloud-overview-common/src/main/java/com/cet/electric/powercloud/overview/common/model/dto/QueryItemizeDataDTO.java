package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 同期对比分析 :能耗项数据返回
 * @date 2023/4/14 13:59
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class QueryItemizeDataDTO {

    @ApiModelProperty("能耗分项名称")
    private String name;
    private Long id;
    private Double value;
    private Double lastValue;
    private Double periodOverPeriodChange;
}
