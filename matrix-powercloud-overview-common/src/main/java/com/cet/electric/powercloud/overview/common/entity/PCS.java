package com.cet.electric.powercloud.overview.common.entity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.cet.electric.powercloud.common.model.device.BaseDeviceModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2023-11-06  19:48
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("pcs")
public class PCS extends BaseDeviceModel {

    @JsonProperty("ratedpower")
    private Double ratedPower;
}
