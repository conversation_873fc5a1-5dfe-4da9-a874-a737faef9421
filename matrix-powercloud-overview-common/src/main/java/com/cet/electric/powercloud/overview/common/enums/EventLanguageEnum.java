package com.cet.electric.powercloud.overview.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: ma<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/3/1 15:08
 * @description:
 */
@AllArgsConstructor
@Getter
public enum EventLanguageEnum {
    EVENT_INFO_MANAGE_CONTROLLER_EXPORTEVENTLIST(9001, "event.info.manage.controller.exportEventList"),
    EVENT_ERROR_EVENT_SERVICE_FOCUSEVENT(9002, "event.error.event.service.focusEvent"),
    EVENT_ERROR_EVENT_SERVICE_CANCELFOCUSEVENT(9003, "event.error.event.service.cancelFocusEvent"),
    EVENT_INFO_EVENT_SERVICE_CONFIRMSELECTEDEVENT(9004, "event.info.event.service.confirmSelectedEvent"),
    EVENT_INFO_EVENT_SERVICE_CONFIRMALLEVENT(9005, "event.info.event.service.confirmAllEvent"),
    EVENT_ERROR_EVENT_SERVICE_GETUNCONFIRMEDEVENTBUILDINGIDS(9006, "event.error.event.service.getUnconfirmedEventBuildingIds"),
    EVENT_ERROR_SYSTEM_SERVICE_GETLOSSSYSTEMEVENT(9007, "event.error.system.service.getLossSystemEvent"),
    EVENT_ERROR_SYSTEM_SERVICE_GETORDERS(9008, "event.error.system.service.getOrders"),
    EVENT_ERROR_EVENT_SERVICE_CONFIRMALLEVENT(9009, "event.error.event.service.confirmAllEvent");

    private final Integer code;

    private final String msg;

    @Override
    public String toString() {
        return "EventLanguageEnum{" + "code=" + code + ", msg='" + msg + '\'' + '}';
    }
}
