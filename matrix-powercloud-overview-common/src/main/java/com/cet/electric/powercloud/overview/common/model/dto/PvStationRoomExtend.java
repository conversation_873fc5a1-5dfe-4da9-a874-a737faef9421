package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 光伏电站扩展属性
 *
 * <AUTHOR>
 * @date 2023/11/15
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class PvStationRoomExtend extends BaseRoomExtend {
    @ApiModelProperty("光伏站类型")
    private Integer pvStationType;
    @ApiModelProperty("站供电类型")
    private Integer powerSupplyType;

}
