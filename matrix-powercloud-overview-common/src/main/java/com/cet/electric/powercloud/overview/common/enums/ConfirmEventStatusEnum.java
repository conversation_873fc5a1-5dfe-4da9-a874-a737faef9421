package com.cet.electric.powercloud.overview.common.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/7 17:08
 */
public enum ConfirmEventStatusEnum {

    UNCONFIRMED(1, "未处理"),
    CONFIRMING(2, "处理中"),
    CONFIRMED(3, "已处理"),
    FILTERED(4, "已过滤");

    private final int code;
    private final String message;

    ConfirmEventStatusEnum(final int code, final String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
