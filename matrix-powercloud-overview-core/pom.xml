<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.cet.electric</groupId>
		<artifactId>matrix-powercloud-overview-parent</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>matrix-powercloud-overview-core</artifactId>
	<version>${revision}</version>
	<dependencies>
		<!-- spring web相关 start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!-- spring web相关 end -->

		<!-- 定时任务-->
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>fusion-matrix-v2-task-core</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.7</version>
		</dependency>
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>matrix-powercloud-common</artifactId>
			<version>${powercloud-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.cet.electric</groupId>
					<artifactId>fusion-matrix-v2-modelsdk</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>fusion-matrix-v2-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>fusion-matrix-v2-client</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>hutool-all</artifactId>
					<groupId>cn.hutool</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>pec-node-service-feign-spring-boot-starter</artifactId>
			<version>2.1.47</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-openfeign-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger-ui</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger2</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>fusion-matrix-v2-modelsdk</artifactId>
			<version>1.0.13-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>matrix-powercloud-overview-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.cet.electric</groupId>
					<artifactId>fusion-matrix-v2-modelsdk</artifactId>
				</exclusion>
				<exclusion>
					<groupId>cn.hutool</groupId>
					<artifactId>hutool-all</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		 <!--3、其他工具-->
        <!--3-1 redis工具-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
		<!-- 注解校验		-->
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>2.0.1.Final</version>
		</dependency>

		<!-- Knife4J		-->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-boot-starter</artifactId>
			<version>2.0.9</version>
		</dependency>

		<dependency>
			<groupId>com.cet.electric</groupId>
			<artifactId>only-report-spring-boot-starter</artifactId>
			<version>3.2.13</version>
			<exclusions>
				<exclusion>
					<groupId>com.cet.electric</groupId>
					<artifactId>device-data-service-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.cet.electric</groupId>
					<artifactId>device-data-service-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.cet.electric</groupId>
					<artifactId>model-service-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.cet.electric</groupId>
					<artifactId>model-service-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
</project>