package com.cet.electric.powercloud.overview.core.api;

import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.cet.electric.powercloud.common.model.Result;
import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.common.model.request.*;
import com.cet.electric.powercloud.overview.common.model.response.PvOverviewRoomInfoResponse;
import com.cet.electric.powercloud.overview.core.service.OverviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Api(value = "/powercloud/overview/api/dashboard/systemOverview", tags = "全局监测接口")
@RestController
@RequestMapping(value = "/powercloud/overview/api/dashboard/systemOverview")
public class OverviewApi {

    @Autowired
    private OverviewService overviewService;

    @ApiOperation(value = "地图信息")
    @PostMapping(value = "/queryOverviewMap")
    public Result<List<OverviewMapParam>> queryOverviewMap(@RequestBody OverviewParam query) {
        return Result.success(overviewService.queryOverviewMap(query));
    }

    @ApiOperation(value = "用电分析")
    @PostMapping(value = "/queryElectricAnalysis")
    public Result<ElectricAnalysisResponse> queryElectricAnalysis(
            @RequestParam(value = "projectId", required = false) Long projectId) {
        return Result.success(overviewService.queryElectricAnalysis(projectId));
    }

    @ApiOperation(value = "本月能耗趋势")
    @PostMapping(value = "/queryMonthUsageCurve")
    public Result<List<DatalogValue>> queryMonthUsageCurve(@RequestBody QueryEnergyConsumptionTrendRequest request) {
        return Result.success(overviewService.queryMonthUsageCurve(request.getProjectId(),
                request.getEnergyType(),
                request.getStartTime(),
                request.getEndTime()));
    }

    @ApiOperation(value = "平台概览和事件概览")
    @PostMapping(value = "/queryPlatformOverview")
    public Result<PlatformOverviewResponse> queryPlatformOverview(@RequestBody OverviewParam query) {
        return Result.success(overviewService.queryPlatformOverview(query));
    }

    @ApiOperation(value = "地图配电房详细信息")
    @PostMapping(value = "/queryOverviewRoomDetail")
    public Result<OverviewMapDetailParam> queryOverviewRoomDetail(@RequestBody OverviewParam query) {
        return Result.success(overviewService.queryOverviewRoomDetail(query));
    }

    @ApiOperation("站点点击详情接口")
    @PostMapping("/overviewRoomInfo")
    public Result<PvOverviewRoomInfoResponse> overviewRoomInfo(@Valid @RequestBody PvOverviewRoomInfo request) {
        return Result.success(overviewService.overviewRoomInfo(request));
    }

    @ApiOperation(value = "尖峰平谷用电量分布")
    @PostMapping("queryMonthPeakVallyDistribute")
    public Result<List<PeakVallyParams>> queryMonthPeakVallyDistribute(@RequestBody QueryMonthPeakVallyDistributeRequest query) {
        return Result.success(overviewService.queryMonthPeakVallyDistribute(
                query.getProjectId(),
                query.getStartTime(),
                query.getEndTime()));
    }

    @ApiOperation(value = "查询分项用能环比接口")
    @PostMapping(value = "/queryTheSameTerm")
    public Result<List<QueryItemizeDataDTO>> queryTheSameTerm(@RequestBody QueryTheSameTermRequest request) {
        //参数校验
        // checkParams(request);
        return Result.success(overviewService.queryTheSameTerm(request));
    }

    @ApiOperation(value = "查询能源类型")
    @PostMapping(value = "/queryEnergyTypes")
    public Result<List<EnergyTypeDTO>> queryEnergyTypes(@RequestBody QueryEnergyTypesRequest request) {
        //参数校验
        // checkParams(request);
        return Result.success(overviewService.queryEnergyTypes(request.getProjectId()));
    }

    @ApiOperation(value = "查询事件类型分布 只返回top5")
    @PostMapping(value = "/queryEventTypeDistribute")
    public Result<List<EventDistributeParam>> queryEventTypeDistribute(@RequestBody EventAnalysisRequest request) {
        return Result.success(overviewService.queryEventTypeDistribute(request.getModelLabel(),
                request.getModelId(),
                request.getStartTime(),
                request.getEndTime(),
                request.getAggregationCycle()));
    }

    @ApiOperation(value = "事件处理及时性")
    @PostMapping(value = "/queryTimeliness")
    public Result<EventTimelinessDTO> queryTimeliness(@RequestBody EventAnalysisRequest request) {
        return Result.success(overviewService.queryTimeliness(request.getModelLabel(),
                request.getModelId(),
                request.getStartTime(),
                request.getEndTime()));
    }
}
