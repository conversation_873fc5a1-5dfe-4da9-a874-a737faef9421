package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.powercloud.overview.common.model.dto.PowerTransformerMeasureBy;

import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
public interface PowerTransformerMeasureInfoService {
    /**
     * 批量查询变压器核心平台设备id
     *
     * @param powerTransformerIds 变压器ids
     * @return 上端、下端、自己的平台设备id
     */
    List<PowerTransformerMeasureBy> getPowerTransformerPecDeviceIdBatch(List<Long> powerTransformerIds);
}
