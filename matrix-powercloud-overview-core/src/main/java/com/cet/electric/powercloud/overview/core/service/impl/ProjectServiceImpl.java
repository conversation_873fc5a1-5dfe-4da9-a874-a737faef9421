package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.PageParams;
import com.cet.electric.powercloud.common.model.device.Contact;
import com.cet.electric.powercloud.common.model.device.Project;
import com.cet.electric.powercloud.common.model.device.Room;
import com.cet.electric.powercloud.overview.common.entity.District;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.common.model.dto.QueryProjectParams;
import com.cet.electric.powercloud.overview.core.mapper.ProjectMapper;
import com.cet.electric.powercloud.overview.core.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private BusinessAuthService businessAuthService;

    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public ListWithTotal<Project> queryProject(QueryProjectParams queryProjectParams) {
        LambdaQueryChainWrapper<Project> projectLambdaQueryChainWrapper = queryProjectWrapper(queryProjectParams);
        projectLambdaQueryChainWrapper
                .join(j -> j.joinModel(Contact.class))
                .join(j -> j.joinModel(Room.class));
        return projectLambdaQueryChainWrapper.listWithTotal();
    }

    private LambdaQueryChainWrapper<Project> queryProjectWrapper(QueryProjectParams queryProjectParams) {
        PageParams pageParams = queryProjectParams.getPageParams();
        List<Long> authProjectIds = businessAuthService.getAuthNodeIds(AuthModelNodesEnum.PROJECT);
        List<Long> ids = queryProjectParams.getIds();
        String name = queryProjectParams.getName();
        Long cityCode = queryProjectParams.getCityCode();
        Long districtCode = queryProjectParams.getDistrictCode();
        Long provinceCode = queryProjectParams.getProvinceCode();
        LambdaQueryChainWrapper<Project> projectLambdaQueryChainWrapper = new LambdaQueryChainWrapper<>(projectMapper);

        if (Objects.nonNull(cityCode)) {
            projectLambdaQueryChainWrapper.eq(Project::getCity, cityCode.toString());
        }
        if (Objects.nonNull(districtCode)) {
            projectLambdaQueryChainWrapper.eq(Project::getDistrict, districtCode.toString());
        }
        if (Objects.nonNull(provinceCode)) {
            projectLambdaQueryChainWrapper.eq(Project::getProvince, provinceCode.toString());
        }
        if (CharSequenceUtil.isNotBlank(name)) {
            projectLambdaQueryChainWrapper.like(Project::getName, name);
        }
        if (CharSequenceUtil.isNotBlank(queryProjectParams.getCode())) {
            projectLambdaQueryChainWrapper.eq(Project::getCode, queryProjectParams.getCode());
        }
        if (CollUtil.isNotEmpty(ids)) {
            projectLambdaQueryChainWrapper.in(Project::getId, ids);
        }
        if (Objects.nonNull(pageParams)) {
            ParamsAssert.notNull(pageParams.getPageSize());
            ParamsAssert.gt(pageParams.getPageSize(), 0);
            ParamsAssert.notNull(pageParams.getPageNum());
            ParamsAssert.gt(pageParams.getPageNum(), 0);
            projectLambdaQueryChainWrapper.page(pageParams.index(), pageParams.size());
        }
        projectLambdaQueryChainWrapper.in(Project::getId, authProjectIds);
        return projectLambdaQueryChainWrapper;
    }
}
