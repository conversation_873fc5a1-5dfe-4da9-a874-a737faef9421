package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.baseconfig.common.dao.MeasuredByMapper;
import com.cet.electric.baseconfig.common.entity.MeasuredBy;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.core.service.DeviceRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class DeviceRelationServiceImpl implements DeviceRelationService {

    @Autowired
    private MeasuredByMapper measuredByMapper;
    @Override
    public List<MeasuredBy> queryDeviceRelationBatch(List<ModelInfo> devices) {
        if (CollUtil.isEmpty(devices)) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<MeasuredBy> measuredByLambdaQueryChainWrapper = new LambdaQueryChainWrapper<>(measuredByMapper);
        List<Long> modelId = devices.stream()
                .map(ModelInfo::getModelId)
                .collect(Collectors.toList());

        List<MeasuredBy> list = measuredByLambdaQueryChainWrapper.in(MeasuredBy::getMonitoredId, modelId).list();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        Map<ModelInfo, ModelInfo> deviceMap = devices.stream()
                .collect(Collectors.toMap(self -> self, self -> self, (k1, k2) -> k1));

        List<MeasuredBy> resultList = new ArrayList<>(list.size());
        for (MeasuredBy measuredBy : list) {
            ModelInfo modelInfo = new ModelInfo(measuredBy.getMonitoredId(), measuredBy.getMonitoredLabel());
            if (Objects.nonNull(deviceMap.get(modelInfo))) {
                resultList.add(measuredBy);
            }
        }
        return resultList;
    }
}
