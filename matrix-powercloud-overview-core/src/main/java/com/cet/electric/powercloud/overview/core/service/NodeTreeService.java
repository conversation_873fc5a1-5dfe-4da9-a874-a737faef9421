package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.baseconfig.sdk.entity.Tree;
import com.cet.electric.baseconfig.sdk.entity.param.LabelAndId;
import com.cet.electric.powercloud.common.model.device.DeviceModel;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.common.model.device.HierarchyNodeTreeVo;
import com.cet.electric.powercloud.common.model.device.QueryCommonTreeRequest;
import com.cet.electric.powercloud.overview.common.entity.QueryCommonNodeTreeParams;

import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/22
 */
public interface NodeTreeService {
    /**
     * 通用节点树
     *
     * @param queryCommonNodeTreeParams user id
     * @return 通用节点树
     */
    List<HierarchyNodeTreeVo> commonNodeTree(QueryCommonNodeTreeParams queryCommonNodeTreeParams);
    /**
     * 通用节点树
     * @param queryCommonNodeTreeRequest
     * @return
     */
    List<Tree> commonNodeTreeOverview(QueryCommonTreeRequest queryCommonNodeTreeRequest);


    /**
     * 增强 baseConfigDeviceService.getEquipmentAccounts(String modelLabel, List<Long> modelIds) 使其支持机器人和摄像机
     */
    List<EquipmentAccount> getEquipmentAccounts(String modelLabel, List<Long> modelIds);

    /**
     * 增强 baseConfigDeviceService.getEquipmentAccounts(String modelLabel, List<Long> modelIds, Long tenantId, Long userId) 使其支持机器人和摄像机
     */
    List<EquipmentAccount> getEquipmentAccounts(String modelLabel, List<Long> modelIds, Long tenantId, Long userId);

    /**
     * 增强 baseConfigDeviceService.getEquipmentAccounts(List<LabelAndId> labelAndIds) 使其支持机器人和摄像机
     */
    List<EquipmentAccount> getEquipmentAccounts(List<LabelAndId> labelAndIds);

    /**
     * 增强 baseConfigDeviceService.getDeviceNodes(String modelLabel, List<Long> modelIds) 使其支持机器人和摄像机
     */
    List<DeviceModel> getDeviceNodes(String modelLabel, List<Long> modelIds);
}
