package com.cet.electric.powercloud.overview.core.service;


import com.cet.electric.powercloud.common.model.device.EquipmentAccount;

import java.util.List;
import java.util.Map;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
public interface HiddenDangerService {
    /**
     * 补充线损查询条件
     *
     * <AUTHOR>
     * @date 2024/1/24
     */
    void supplementarySystemEventQueryConditions(List<Integer> eventTypes,
                                                 Map<String, List<Long>> labelToIdsMap,
                                                 List<EquipmentAccount> equipmentAccounts);
}
