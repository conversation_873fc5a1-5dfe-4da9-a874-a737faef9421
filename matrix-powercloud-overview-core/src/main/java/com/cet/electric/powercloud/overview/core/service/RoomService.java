package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.powercloud.common.model.device.Room;
import com.cet.electric.powercloud.overview.common.model.dto.QueryRoomParams;

import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
public interface RoomService {
    /**
     * 查询节点下的权限房间
     *
     * @param authIds  PROJECT, BUILDING, FLOOR
     * @return 房间分页对象
     */
    List<Room> queryAuthRoomByProjectIds(List<Long> authIds, String modelLabel);

    /**
     * 按照id查询房间
     *
     * @param id
     */
    Room queryRoomById(Long id);
    /**
     * 通用查询房间
     *
     * @param queryRoomParams 通用查询房间参数
     * @return 房间分页对象
     */
    ListWithTotal<Room> queryRoom(QueryRoomParams queryRoomParams);

}
