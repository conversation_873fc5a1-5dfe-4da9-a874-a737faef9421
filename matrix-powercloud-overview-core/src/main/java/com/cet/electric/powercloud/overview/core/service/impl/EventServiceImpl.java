package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.modelservice.common.query.ModelQuery;
import com.cet.electric.modelservice.core.query.impl.ModelExtendService;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper;
import com.cet.electric.powercloud.common.client.model.SystemEvent;
import com.cet.electric.powercloud.common.exception.BusinessException;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.enums.ConfirmEventStatusEnum;
import com.cet.electric.powercloud.overview.common.enums.EventLanguageEnum;
import com.cet.electric.powercloud.overview.common.enums.NodeEventProcessingStageEnum;
import com.cet.electric.powercloud.overview.common.model.dto.DeviceAlarmGroupCount;
import com.cet.electric.powercloud.overview.core.service.DeviceService;
import com.cet.electric.powercloud.overview.core.service.EventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class EventServiceImpl implements EventService {

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ModelExtendService modelExtendService;
    @Override
    public Map<Long, Integer> getUnconfirmedEventRoomIds(Long startTime, Long endTime, List<Long> roomIds) {
        List<EquipmentAccount> deviceList = deviceService.queryDeviceByHierarchy(TableNameConstant.ROOM, roomIds, true);
        if (CollUtil.isEmpty(deviceList)){
            return Collections.emptyMap();
        }
        Map<String, List<Long>> labelToIdsMap = deviceList.stream()
                .collect(
                        Collectors.groupingBy(EquipmentAccount::getModelLabel,
                                Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        @SuppressWarnings("unchecked")
        LambdaQueryWrapper<SystemEvent> wrapper = new LambdaQueryWrapper<>(SystemEvent.class)
                .groupBy(SystemEvent::getObjectId, SystemEvent::getObjectLabel, SystemEvent::getConfirmEventStatus);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            wrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                    .between(SystemEvent::getEventTime, startTime, endTime)
                    .in(SystemEvent::getObjectId, entry.getValue())
                    .in(SystemEvent::getConfirmEventStatus,
                            Arrays.asList(
                                    ConfirmEventStatusEnum.UNCONFIRMED.getCode(),
                                    ConfirmEventStatusEnum.CONFIRMING.getCode()))).or();
        }
        ModelQuery modelQuery = wrapper.orderByAsc(SystemEvent::getEventTime).build();
        ApiResult<List<DeviceAlarmGroupCount>> eventCountResult = modelExtendService.queryModelData(modelQuery, DeviceAlarmGroupCount.class);
        if (!Boolean.TRUE.equals(eventCountResult.isSuccess())) {
            throw new BusinessException(EventLanguageEnum.EVENT_ERROR_EVENT_SERVICE_GETUNCONFIRMEDEVENTBUILDINGIDS, eventCountResult.getMsg());
        }
        if (CollUtil.isEmpty(eventCountResult.getData())) {
            return Collections.emptyMap();
        }
        // 过滤出线损事件
        Map<Integer, List<DeviceAlarmGroupCount>> statusToEquipCountsMap = eventCountResult.getData().stream()
                .filter(item -> Objects.equals(item.getDeviceLabel(), TableNameConstant.EQUIPMENT_ACCOUNT))
                .collect(Collectors.groupingBy(DeviceAlarmGroupCount::getConfirmEventStatus));
        Map<Long, List<EquipmentAccount>> roomIdToEquipmentsMap = deviceList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getRoomId));
        // 过滤出线损以外的事件
        Map<Integer, List<DeviceAlarmGroupCount>> statusToCountsMap = eventCountResult.getData().stream()
                .filter(item -> !Objects.equals(item.getDeviceLabel(), TableNameConstant.EQUIPMENT_ACCOUNT))
                .collect(Collectors.groupingBy(DeviceAlarmGroupCount::getConfirmEventStatus));
        Map<Long, List<ModelInfo>> roomIdToModelsMap = deviceList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getRoomId,
                        Collectors.mapping(item -> new ModelInfo(item.getId(), item.getModelLabel()), Collectors.toList())));
        Map<Long, Integer> roomAndStatusMap = new HashMap<>();
        for (Long roomId : roomIds) {
            long unconfirmCount = 0;
            long confirmingCount = 0;
            // 返回的分组结果中包含多种模型
            // 计算线损事件数量
            if (CollUtil.isNotEmpty(statusToEquipCountsMap)) {
                List<Long> equipmentIds = roomIdToEquipmentsMap.getOrDefault(roomId, Collections.emptyList())
                        .stream().map(EquipmentAccount::getId).collect(Collectors.toList());
                unconfirmCount += statusToEquipCountsMap.getOrDefault(ConfirmEventStatusEnum.UNCONFIRMED.getCode(), Collections.emptyList())
                        .stream().filter(item -> equipmentIds.contains(item.getDeviceId()))
                        .mapToLong(DeviceAlarmGroupCount::getCount).sum();
                confirmingCount += statusToEquipCountsMap.getOrDefault(ConfirmEventStatusEnum.CONFIRMING.getCode(), Collections.emptyList())
                        .stream().filter(item -> equipmentIds.contains(item.getDeviceId()))
                        .mapToLong(DeviceAlarmGroupCount::getCount).sum();
            }
            // 根据除线损以外的事件判断
            List<ModelInfo> models = roomIdToModelsMap.getOrDefault(roomId, Collections.emptyList());
            unconfirmCount += statusToCountsMap.getOrDefault(ConfirmEventStatusEnum.UNCONFIRMED.getCode(), Collections.emptyList())
                    .stream().filter(item -> models.contains(new ModelInfo(item.getDeviceId(), item.getDeviceLabel())))
                    .mapToLong(DeviceAlarmGroupCount::getCount).sum();
            confirmingCount += statusToCountsMap.getOrDefault(ConfirmEventStatusEnum.CONFIRMING.getCode(), Collections.emptyList())
                    .stream().filter(item -> models.contains(new ModelInfo(item.getDeviceId(), item.getDeviceLabel())))
                    .mapToLong(DeviceAlarmGroupCount::getCount).sum();
            roomAndStatusMap.put(roomId, NodeEventProcessingStageEnum.getEventStatus(unconfirmCount, confirmingCount));
        }
        return roomAndStatusMap;
    }
}
