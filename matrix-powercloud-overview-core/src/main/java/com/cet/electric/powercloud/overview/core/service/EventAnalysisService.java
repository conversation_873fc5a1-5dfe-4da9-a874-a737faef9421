package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.powercloud.overview.common.model.dto.EventAnalysisParams;
import com.cet.electric.powercloud.overview.common.model.dto.EventOverviewParam;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
public interface EventAnalysisService {

    /**
     * 查询业务事件 : 告警总数,已处理 ,未处理数量
     * @param query 查询参数
     * @return EventOverviewParam
     */
    EventOverviewParam queryEventMatchOverview(EventAnalysisParams query);
}
