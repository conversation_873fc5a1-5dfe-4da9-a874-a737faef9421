package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.DeviceDataIdLogicalId;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.MeasurePointData;
import com.cet.electric.powercloud.common.client.DeviceDataExtendService;
import com.cet.electric.powercloud.overview.common.constants.SymbolConstant;
import com.cet.electric.powercloud.overview.common.entity.PowerTransformer;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.common.model.dto.PecRealDateQueryDTO;
import com.cet.electric.powercloud.overview.common.model.dto.PecRealTimeDataDTO;
import com.cet.electric.powercloud.overview.common.model.dto.PowerTransformerMeasureBy;
import com.cet.electric.powercloud.overview.core.mapper.PowerTransformerMapper;
import com.cet.electric.powercloud.overview.core.service.PowerTransformerMeasureInfoService;
import com.cet.electric.powercloud.overview.core.service.TransformerAlgorithmService;
import com.cet.electric.powercloud.overview.core.service.TransformerMonitoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class TransformerMonitoringServiceImpl implements TransformerMonitoringService {

    @Autowired
    private PowerTransformerMeasureInfoService powerTransformerMeasureInfoService;
    @Autowired
    private DeviceDataExtendService deviceDataExtendService;

    @Autowired
    private PowerTransformerMapper powerTransformerMapper;

    @Autowired
    private TransformerAlgorithmService algorithmService;

    @Override
    public Map<String, MeasurePointData> getRealDataToDeviceMap(List<Long> ids, List<Long> dataIds) {
        ParamsAssert.notEmpty(ids);
        ParamsAssert.notEmpty(dataIds);
        Map<String, MeasurePointData> resultMap = new HashMap<>();
        List<PowerTransformerMeasureBy> power = powerTransformerMeasureInfoService
                .getPowerTransformerPecDeviceIdBatch(ids);
        if (CollUtil.isEmpty(power)) {
            return resultMap;
        }
        List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = new ArrayList<>();
        List<Long> measureBys = new ArrayList<>();
        power.forEach(x -> {
            if (Objects.nonNull(x.getInflowMeasureBy()) && !measureBys.contains(x.getInflowMeasureBy())) {
                measureBys.add(x.getInflowMeasureBy());
            }
            if (Objects.nonNull(x.getOutflowMeasureBy()) && !measureBys.contains(x.getOutflowMeasureBy())) {
                measureBys.add(x.getOutflowMeasureBy());
            }
            if (Objects.nonNull(x.getMeasureBy()) && !measureBys.contains(x.getMeasureBy())) {
                measureBys.add(x.getMeasureBy());
            }
        });
        measureBys.forEach(x -> dataIds.forEach(y -> {
                    DeviceDataIdLogicalId deviceDataIdLogicalId=new DeviceDataIdLogicalId();
                    deviceDataIdLogicalId.setDeviceId(x.intValue());
                    deviceDataIdLogicalId.setDataId(y.intValue());
                    deviceDataIdLogicalId.setLogicalId(1);
                    pecRealDateQueryDTOS.add(deviceDataIdLogicalId);
                }));
        Map<String, MeasurePointData> map = deviceDataExtendService.batchRealTimeData(pecRealDateQueryDTOS).stream()
                .collect(Collectors.toMap(x -> x.getDeviceId() + SymbolConstant.UNDER_SCORE + x.getDataId().toString(), x -> x));
        power.forEach(x -> dataIds.forEach(y -> {
            if (Objects.nonNull(x.getInflowMeasureBy()) && Objects.nonNull(map.get(x.getInflowMeasureBy() + SymbolConstant.UNDER_SCORE + y))) {
                resultMap.put(x.getPowerTransformerId().toString() + SymbolConstant.UNDER_SCORE + y, map.get(x.getInflowMeasureBy() + SymbolConstant.UNDER_SCORE + y));
            } else if (Objects.nonNull(x.getOutflowMeasureBy()) && Objects.nonNull(map.get(x.getOutflowMeasureBy() + SymbolConstant.UNDER_SCORE + y))) {
                resultMap.put(x.getPowerTransformerId().toString() + SymbolConstant.UNDER_SCORE + y, map.get(x.getOutflowMeasureBy() + SymbolConstant.UNDER_SCORE + y));
            } else if (Objects.nonNull(x.getMeasureBy()) && Objects.nonNull(map.get(x.getMeasureBy() + SymbolConstant.UNDER_SCORE + y))) {
                resultMap.put(x.getPowerTransformerId().toString() + SymbolConstant.UNDER_SCORE + y, map.get(x.getMeasureBy() + SymbolConstant.UNDER_SCORE + y));
            }
        }));
        return resultMap;
    }

    @Override
    public Double queryLoadFactor(List<Long> ids, Map<Long, Double> apparentPowerMap) {

        List<PowerTransformer> powerTransformerList = powerTransformerMapper.selectBatchIds(ids);
        List<Double> valueList = new ArrayList<>();
        powerTransformerList.forEach(x -> {
            Double ratedCapacity = algorithmService.getRatedCapacity(x);
            if (Objects.nonNull(apparentPowerMap.get(x.getId()))
                    && ratedCapacity != null
                    && ratedCapacity != 0) {
                valueList.add((apparentPowerMap.get(x.getId()) / ratedCapacity) * 100);
            }
        });
        if (CollUtil.isNotEmpty(valueList)) {
            return (valueList.stream().reduce(0.0, Double::sum)) / valueList.size();
        } else {
            return null;
        }
    }
}
