package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.baseconfig.sdk.entity.Tree;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.matrix.BaseConfigDeviceService;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.device.*;
import com.cet.electric.powercloud.common.utils.AuthUtil;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.PvEnergyContainer;
import com.cet.electric.powercloud.overview.common.entity.QueryCommonNodeTreeParams;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.core.mapper.PowerDisCabinetMapper;
import com.cet.electric.powercloud.overview.core.mapper.PvEnergyContainerMapper;
import com.cet.electric.powercloud.overview.core.service.DeviceService;
import com.cet.electric.powercloud.overview.core.service.NodeTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class DeviceServiceImpl implements DeviceService {
    @Autowired
    private BusinessAuthService userService;

    @Autowired
    private NodeTreeService nodeTreeService;


    @Autowired
    private PowerDisCabinetMapper powerDisCabinetMapper;

    @Autowired
    private PvEnergyContainerMapper pvEnergyContainerMapper;

    @Autowired
    private BaseConfigDeviceService baseConfigDeviceService;

    @Override
    public List<EquipmentAccount> queryDeviceByHierarchy(String modelLabel, List<Long> modelIds, Boolean isAuth) {
        ParamsAssert.notNull(modelLabel);
        ParamsAssert.notEmpty(modelIds);
        if (TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(modelLabel) && Objects.equals(isAuth, Boolean.TRUE)) {
            // 过滤出该节点下有权限的房间
            List<Tree> rooms = underLabelRooms(modelLabel, modelIds, Collections.emptyList());
            if (CollUtil.isEmpty(rooms)) {
                return Collections.emptyList();
            }
            modelLabel = TableNameConstant.ROOM;
            modelIds = rooms.stream().map(Tree::getId).collect(Collectors.toList());
        }
        List<EquipmentAccount> equipmentAccountList = new ArrayList<>();
        switch (modelLabel) {
            case TableNameConstant.PROJECT:
                equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.PROJECT,modelIds);
                break;
            case TableNameConstant.ROOM:
                equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.ROOM,modelIds);
                break;
            case TableNameConstant.BUILDING:
                equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.BUILDING,modelIds);
                break;
            case TableNameConstant.FLOOR:
                equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.FLOOR,modelIds);
                break;
            case TableNameConstant.POWER_DIS_CABINET:
                List<PowerDisCabinet> powerDisCabinets = new LambdaQueryChainWrapper<>(powerDisCabinetMapper)
                        .in(PowerDisCabinet::getId, modelIds)
                        .join(j -> j.joinModel(TableNameConstant.LINE_SEGMENT))
                        .join(j -> j.joinModel(TableNameConstant.CIRCUIT_BREAKER))
                        .list();
                if (Objects.isNull(powerDisCabinets)) {
                    return Collections.emptyList();
                }
                List<Long> lineIds = powerDisCabinets.stream().flatMap(p -> CollUtil.isEmpty(p.getLineSegmentList()) ? Stream.empty() : p.getLineSegmentList().stream())
                        .map(BaseDeviceModel::getId)
                        .collect(Collectors.toList());
                List<Long> circuitBreakerIds = powerDisCabinets.stream().flatMap(p -> CollUtil.isEmpty(p.getCircuitBreakerList()) ? Stream.empty() : p.getCircuitBreakerList().stream())
                        .map(BaseDeviceModel::getId)
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(lineIds)) {
                    equipmentAccountList.addAll(baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.LINE_SEGMENT,lineIds));
                }
                if (CollUtil.isNotEmpty(circuitBreakerIds)) {
                    equipmentAccountList.addAll(baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.CIRCUIT_BREAKER,circuitBreakerIds));
                }
                break;
            case TableNameConstant.PV_ENERGY_CONTAINER:
                List<PvEnergyContainer> pvEnergyContainers = new LambdaQueryChainWrapper<>(pvEnergyContainerMapper)
                        .in(PvEnergyContainer::getId, modelIds)
                        .join(j -> j.joinModel(TableNameConstant.PCS))
                        .join(j -> j.joinModel(TableNameConstant.PV_ACCUMULATOR))
                        .list();
                if (CollUtil.isEmpty(pvEnergyContainers)) {
                    return Collections.emptyList();
                }
                List<Long> pcsIds = pvEnergyContainers.stream().flatMap(p -> CollUtil.isEmpty(p.getPcsList()) ? Stream.empty() : p.getPcsList().stream())
                        .map(BaseDeviceModel::getId)
                        .collect(Collectors.toList());
                List<Long> accumulatorIds = pvEnergyContainers.stream().flatMap(p -> CollUtil.isEmpty(p.getPvAccumulatorList()) ? Stream.empty() : p.getPvAccumulatorList().stream())
                        .map(BaseDeviceModel::getId)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(pcsIds)) {
                    equipmentAccountList.addAll(baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.PCS,pcsIds));
                }
                if (CollUtil.isNotEmpty(accumulatorIds)) {
                    equipmentAccountList.addAll(baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.PV_ACCUMULATOR,accumulatorIds));
                }
                break;
            default:
                equipmentAccountList.addAll(baseConfigDeviceService.getEquipmentAccounts(modelLabel,modelIds));
        }
        return equipmentAccountList;
    }

    @Override
    public List<Tree> underLabelRooms(String modelLabel, List<Long> modelIds, List<Integer> roomTypes) {
        if (TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(modelLabel)) {
            // 房间节点直接封装返回
            if (Objects.equals(TableNameConstant.ROOM, modelLabel)) {
                return modelIds.stream().map(it -> {
                    Tree room = new Tree();
                    room.setId(it);
                    room.setModelLabel(TableNameConstant.ROOM);
                    return room;
                }).collect(Collectors.toList());
            }
            // 过滤出该节点下有权限的房间
            QueryCommonTreeRequest queryCommonTreeRequest = new QueryCommonTreeRequest();
            queryCommonTreeRequest.setRootLabel(modelLabel);
            queryCommonTreeRequest.setRootIds(modelIds);
            queryCommonTreeRequest.setSubLabelList(Collections.singletonList(TableNameConstant.ROOM));
            queryCommonTreeRequest.setRoomTypes(roomTypes);
            List<Tree> models = baseConfigDeviceService.queryCommonNodeTree(AuthUtil.getCurrentTenant(), AuthUtil.getUserId(), queryCommonTreeRequest);
            return models.stream()
                    .flatMap(item -> CollUtil.isEmpty(item.getChildren()) ? Stream.empty() : item.getChildren().stream())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
