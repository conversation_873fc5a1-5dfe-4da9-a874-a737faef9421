package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.powercloud.common.client.model.CamerasDTO;
import com.cet.electric.powercloud.overview.core.service.impl.VideoManageFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @discription
 * @date 2024/10/12 下午4:11
 */
public abstract class AbstractVideoManageService {

    @Autowired
    protected VideoManageFactory videoManageFactory;

    @PostConstruct
    public void init() {
        videoManageFactory.videoManageRegister(this);
    }

    /**
     * 获取摄像头类型
     * @return 摄像头类型
     */
    public abstract String getCameraType();

    /**
     * 获取摄像头类型名称
     * @return 摄像头类型名称
     */
    public abstract String getCameraTypeName();

    /**
     * 摄像头同步
     *
     * @return 返回摄像头同步结果
     */
    public abstract List<CamerasDTO> cameraAutoUpdate();

    /**
     * 获取摄像头播放地址
     *
     * @return 获取摄像头播放地址
     */
    public abstract String getCameraUrl(CamerasDTO camera);

    /**
     * 摄像头控制
     */
    public abstract void controlsCamera(CamerasDTO camera, Integer controlType);
}
