package com.cet.electric.powercloud.overview.core.service;

import java.util.List;
import java.util.Map;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
public interface EventService {
    /**
     * 查询有未处理事件的roomId
     *
     * @param startTime startTime
     * @param endTime   endTime
     * @param roomIds   roomIds
     * @return roomIds
     */
    Map<Long, Integer> getUnconfirmedEventRoomIds(Long startTime, Long endTime, List<Long> roomIds);
}
