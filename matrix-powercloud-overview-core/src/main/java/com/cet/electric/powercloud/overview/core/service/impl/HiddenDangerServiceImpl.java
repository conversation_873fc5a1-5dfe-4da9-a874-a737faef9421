package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.powercloud.common.enums.common.SystemEventTypeEnum;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.core.service.HiddenDangerService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class HiddenDangerServiceImpl implements HiddenDangerService {
    @Override
    public void supplementarySystemEventQueryConditions(List<Integer> eventTypes, Map<String, List<Long>> labelToIdsMap, List<EquipmentAccount> equipmentAccounts) {
        if ((CollUtil.isEmpty(eventTypes)
                || CollUtil.contains(eventTypes, SystemEventTypeEnum.HOUR_LOSS_ALERT.getCode())
                || CollUtil.contains(eventTypes, SystemEventTypeEnum.DAY_LOSS_ALERT.getCode()))
                && CollUtil.isNotEmpty(equipmentAccounts)) {
            // 补充线损事件
            labelToIdsMap.put(TableNameConstant.EQUIPMENT_ACCOUNT, equipmentAccounts.stream().map(EquipmentAccount::getId).collect(Collectors.toList()));
        }
    }
}
