package com.cet.electric.powercloud.overview.core.service.impl;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cet.electric.powercloud.common.client.VideoExtendService;
import com.cet.electric.powercloud.common.client.model.CamerasDTO;
import com.cet.electric.powercloud.common.enums.DefaultCodeAndMsg;
import com.cet.electric.powercloud.common.exception.BusinessException;
import com.cet.electric.powercloud.overview.common.model.dto.CameraTypeVo;
import com.cet.electric.powercloud.overview.core.service.AbstractVideoManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @discription 视频管理工厂
 * @date 2024/10/14 上午11:23
 */
@Component
public class VideoManageFactory {

    @Autowired
    private VideoExtendService videoExtendService;

    private final Map<String, AbstractVideoManageService> VIDEO_MANAGE_MAP = new ConcurrentHashMap<>();
    private final Vector<CameraTypeVo> VIDEO_TYPE_LIST = new Vector<>();

    /**
     * 摄像头控制
     */
    public void controlsCamera(Long id, Integer controlType) {
        CamerasDTO camerasDTO = queryCamera(id);
        AbstractVideoManageService abstractVideoManageService = getVideoManageService(camerasDTO.getJoinType());
        if (Objects.isNull(abstractVideoManageService)){
            return;
        }
        abstractVideoManageService.controlsCamera(camerasDTO,controlType);
    }
    private AbstractVideoManageService getVideoManageService(String cameraType) {
        return VIDEO_MANAGE_MAP.get(cameraType);
    }

    /**
     * 获取摄像头播放地址
     *
     * @return 获取摄像头播放地址
     */
    public String getCameraUrl(Long id) {
        CamerasDTO camerasDTO = queryCamera(id);
        AbstractVideoManageService abstractVideoManageService = getVideoManageService(camerasDTO.getJoinType());
        if (Objects.isNull(abstractVideoManageService)){
            JSONObject playUrl = JSONUtil.parseObj(camerasDTO.getPlayUrl());
            return playUrl.get("hls",String.class);
        }
        return abstractVideoManageService.getCameraUrl(camerasDTO);
    }


    public void videoManageRegister(AbstractVideoManageService abstractVideoManageService){
        VIDEO_MANAGE_MAP.put(abstractVideoManageService.getCameraType(), abstractVideoManageService);
        VIDEO_TYPE_LIST.add(new CameraTypeVo(abstractVideoManageService.getCameraType(), abstractVideoManageService.getCameraTypeName()));
    }

    private CamerasDTO queryCamera(Long id){
        Optional<CamerasDTO> first = videoExtendService.cameras().stream()
                .filter(c -> Objects.equals(c.getId(), id))
                .findFirst();
        if (!first.isPresent()){
            throw new BusinessException(DefaultCodeAndMsg.NOT_FOUND);
        }
        return first.get();
    }


}
