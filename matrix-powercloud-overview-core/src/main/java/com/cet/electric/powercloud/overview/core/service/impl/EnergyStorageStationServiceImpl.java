package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.cet.electric.baseconfig.common.entity.MeasuredBy;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.DeviceDataIdLogicalId;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.MeasurePointData;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.client.DeviceDataExtendService;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.enums.common.QuantityDataEnum;
import com.cet.electric.powercloud.common.enums.device.RoomTypeEnum;
import com.cet.electric.powercloud.common.exception.BusinessException;
import com.cet.electric.powercloud.common.matrix.BaseConfigDeviceService;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.device.BaseDeviceModel;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.common.model.device.Room;
import com.cet.electric.powercloud.common.utils.CommonUtil;
import com.cet.electric.powercloud.common.utils.Json;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.entity.PvAccumulator;
import com.cet.electric.powercloud.overview.common.entity.PvEnergyContainer;
import com.cet.electric.powercloud.overview.common.enums.PhotoVoltaicLanguageEnum;
import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.core.mapper.PvEnergyContainerMapper;
import com.cet.electric.powercloud.overview.core.service.DeviceRelationService;
import com.cet.electric.powercloud.overview.core.service.EnergyStorageStationService;
import com.cet.electric.powercloud.overview.core.service.RoomService;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
@AllArgsConstructor
public class EnergyStorageStationServiceImpl implements EnergyStorageStationService {
    private final RoomService roomService;
    private final PvEnergyContainerMapper pvEnergyContainerMapper;
    private final DeviceRelationService deviceRelationService;
    private final DeviceDataExtendService deviceDataExtendService;
    private final BusinessAuthService businessAuthService;
    private final BaseConfigDeviceService baseConfigDeviceService;

    @Override
    public List<EnergyStorageStationRankingDTO> energyStorageStationEAFRanking(Long projectId, Long roomIdStorage) {
        List<Room> roomList;
        if (Objects.nonNull(roomIdStorage)) {
            Room room = roomService.queryRoomById(roomIdStorage);
            roomList = Collections.singletonList(room);
        } else {
            roomList = getRoomList(projectId, TableNameConstant.PROJECT);
        }
        if (CollUtil.isEmpty(roomList)) {
            return Collections.emptyList();
        }
        List<EnergyStorageStationRankingDTO> result = new ArrayList<>(roomList.size());
        for (Room room : roomList) {
            EnergyStorageStationRankingDTO dto = new EnergyStorageStationRankingDTO();
            dto.setRoomId(room.getId());
            dto.setName(room.getName());
            result.add(dto);
        }
        // 房间id与房间的映射
        Map<Long, Room> roomIdToRoomMap = roomList.stream().collect(Collectors.toMap(Room::getId, Function.identity()));

        List<EquipmentAccount> equipmentAccountList =baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.ROOM,new ArrayList<>(roomIdToRoomMap.keySet()))
                .stream()
                .filter(it->Objects.equals(it.getModelLabel(),TableNameConstant.PV_ENERGY_CONTAINER)||Objects.equals(it.getModelLabel(),TableNameConstant.PV_ACCUMULATOR))
                .collect(Collectors.toList());
        List<EquipmentAccount> containerEquipmentList = equipmentAccountList
                .stream()
                .filter(item -> Objects.equals(TableNameConstant.PV_ENERGY_CONTAINER, item.getModelLabel()))
                .collect(Collectors.toList());
        // 房间与房间下的储能集装箱的map
        Map<Long, List<EquipmentAccount>> roomToEnergyContainerGroup = containerEquipmentList
                .stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getRoomId));

        List<EquipmentAccount> accumulatorList = equipmentAccountList
                .stream()
                .filter(item -> Objects.equals(TableNameConstant.PV_ACCUMULATOR, item.getModelLabel()))
                .collect(Collectors.toList());

        // 得到储能集装箱与蓄电池的映射
        List<PvEnergyContainer> energyContainerList = new LambdaQueryChainWrapper<>(pvEnergyContainerMapper)
                .in(BaseDeviceModel::getId, CollStreamUtil.toList(containerEquipmentList, EquipmentAccount::getId))
                .join(j -> j.joinModel(TableNameConstant.PV_ACCUMULATOR))
                .list();
        Map<Long, Long> accumulatorToContainerMap = new HashMap<>();
        for (PvEnergyContainer pvEnergyContainer : energyContainerList) {
            List<PvAccumulator> pvAccumulatorList = pvEnergyContainer.getPvAccumulatorList();
            if (CollUtil.isNotEmpty(pvAccumulatorList)) {
                for (PvAccumulator pvAccumulator : pvAccumulatorList) {
                    accumulatorToContainerMap.put(pvAccumulator.getId(), pvEnergyContainer.getId());
                }
            }
        }
        List<Long> accumulatorIds = accumulatorList.stream().map(EquipmentAccount::getId).collect(Collectors.toList());

        List<MeasuredBy> accumulatorMeasuredByList = deviceRelationService.queryDeviceRelationBatch(
                accumulatorIds.stream()
                        .map(id -> new ModelInfo(id, TableNameConstant.PV_ACCUMULATOR))
                        .collect(Collectors.toList()));

        Map<Integer, Long> measuredbyToAccumulatorIdMap = accumulatorMeasuredByList.stream().collect(Collectors.toMap(MeasuredBy::getMeasuredBy, MeasuredBy::getMonitoredId, (v1, v2) -> v1));
        // 被分组的dataId
        List<Long> dataIds = Arrays.asList(
                QuantityDataEnum.ENERGY_STORAGE_TOTAL_CHARGE.getDataId().longValue(),
                QuantityDataEnum.ENERGY_STORAGE_TOTAL_DISCHARGE.getDataId().longValue()
        );

        List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = getPecRealDateQueryDTOS(accumulatorMeasuredByList, dataIds);
        List<MeasurePointData> pecRealTimeDataDTOS = deviceDataExtendService.batchRealTimeData(pecRealDateQueryDTOS);
        // 根据dataId和所属集装箱来分组并求和value
        Map<Integer, Map<Long, Double>> dataIdToContainerIdToValueMap = pecRealTimeDataDTOS
                .stream()
                .filter(item -> Objects.nonNull(item.getValue()))
                .collect(
                        Collectors.groupingBy(MeasurePointData::getDataId,
                                Collectors.groupingBy(it -> accumulatorToContainerMap.get(measuredbyToAccumulatorIdMap.get(it.getDeviceId())),
                                        Collectors.summingDouble(MeasurePointData::getValue))));

        for (EnergyStorageStationRankingDTO dto : result) {
            Long roomId = dto.getRoomId();
            Room room = roomIdToRoomMap.get(roomId);
            // 获得接入平台到目前为止的小时数
            if (Objects.isNull(room.getCommissionDate())) {
                continue;
            }
            List<EquipmentAccount> containerList = roomToEnergyContainerGroup.get(roomId);
            if (CollUtil.isNotEmpty(containerList)) {
                Double hour = NumberUtil.div((System.currentTimeMillis() - room.getCommissionDate()), 3600000);
                List<Double> unitFactors = containerList.stream().map(equipment -> {
                            Long containerId = equipment.getId();
                            Optional<Double> chargeOptional = Optional.ofNullable(dataIdToContainerIdToValueMap.get(QuantityDataEnum.ENERGY_STORAGE_TOTAL_CHARGE.getDataId()))
                                    .map(map -> map.get(containerId));
                            Optional<Double> dischargeOptional = Optional.ofNullable(dataIdToContainerIdToValueMap.get(QuantityDataEnum.ENERGY_STORAGE_TOTAL_DISCHARGE.getDataId()))
                                    .map(map -> map.get(containerId));
                            if (!chargeOptional.isPresent() || !dischargeOptional.isPresent()) {
                                return null;
                            }
                            Double charge = dataIdToContainerIdToValueMap.get(QuantityDataEnum.ENERGY_STORAGE_TOTAL_CHARGE.getDataId()).get(containerId);
                            Double discharge = dataIdToContainerIdToValueMap.get(QuantityDataEnum.ENERGY_STORAGE_TOTAL_DISCHARGE.getDataId()).get(containerId);
                            return unitEquivalentUtilizationFactor(charge, discharge, 1D, hour);
                        }
                ).collect(Collectors.toList());
                Double unitFactor = CommonUtil.calculateDoublesSum(unitFactors);
                Double totalRatedPower = room.getTotalRatedPower();
                dto.setValue(stationUnitEquivalentUtilizationFactor(unitFactor, totalRatedPower));
            }
        }

        return result.stream()
                .sorted(Comparator.comparing(EnergyStorageStationRankingDTO::getValue, Comparator.nullsLast(Comparator.reverseOrder())))
                .limit(5)
                .collect(Collectors.toList());
    }

    /**
     * 储能单元等效利用系数计算
     */
    private Double unitEquivalentUtilizationFactor(Double eci, Double edi, Double pi, Double ph) {
        if (Objects.isNull(eci) || Objects.isNull(edi) || Objects.isNull(pi) || Objects.isNull(ph)) {
            return null;
        }
        double numerator = NumberUtil.add(eci, edi);
        double denominator = NumberUtil.mul(pi, ph);
        if (Double.compare(denominator, 0.0) == 0) {
            return null;
        }
        double result = NumberUtil.div(numerator, denominator);
        return NumberUtil.round(result, 4).doubleValue();
    }

    private Double stationUnitEquivalentUtilizationFactor(Double sum, Double p) {
        if (Objects.isNull(sum) || Objects.isNull(p)) {
            return null;
        }
        if (Double.compare(p, 0.0) == 0) {
            return null;
        }
        double result = NumberUtil.div(sum, p) * 100;
        return NumberUtil.round(result, 2).doubleValue();
    }

    private List<DeviceDataIdLogicalId> getPecRealDateQueryDTOS(Collection<MeasuredBy> measuredByList, Collection<Long> dataIds) {
        // 查询测点实时数据
        List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = new ArrayList<>(measuredByList.size() * dataIds.size());
        for (MeasuredBy measuredBy : measuredByList) {
            for (Long dataId : dataIds) {
                DeviceDataIdLogicalId deviceDataIdLogicalId = new DeviceDataIdLogicalId();
                deviceDataIdLogicalId.setLogicalId(1);
                deviceDataIdLogicalId.setDataId(dataId.intValue());
                deviceDataIdLogicalId.setDeviceId(measuredBy.getMeasuredBy());
                pecRealDateQueryDTOS.add(deviceDataIdLogicalId);
            }
        }
        return pecRealDateQueryDTOS;
    }

    private List<Room> getRoomList(Long modelId, String modelLabel) {
        QueryRoomParams queryRoomParams = new QueryRoomParams();
        queryRoomParams.setRoomType(RoomTypeEnum.ENERGY_STORAGE_STATION.getCode());
        ListWithTotal<Room> roomListWithTotal;
        switch (modelLabel) {
            case TableNameConstant.PROJECT:
                if (Objects.nonNull(modelId)) {
                    queryRoomParams.setProjectId(modelId);
                }
                roomListWithTotal = roomService.queryRoom(queryRoomParams);
                break;
            case TableNameConstant.ROOM:
                queryRoomParams.setIds(Collections.singletonList(modelId));
                roomListWithTotal = roomService.queryRoom(queryRoomParams);
                break;
            default:
                throw new BusinessException(LanguageUtil.getMessage(PhotoVoltaicLanguageEnum.ERROR_ESS_HIERARCHY.getMsg()));
        }
        if (businessAuthService.isSystemManager()) {
            return roomListWithTotal.getList();
        }
        List<Long> authRoomIds = businessAuthService.getAuthNodeIds(AuthModelNodesEnum.ROOM);
        return roomListWithTotal.getList().stream()
                .filter(item -> CollUtil.contains(authRoomIds, item.getId()))
                .collect(Collectors.toList());
    }
}
