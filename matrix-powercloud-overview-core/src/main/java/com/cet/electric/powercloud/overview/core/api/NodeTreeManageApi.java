package com.cet.electric.powercloud.overview.core.api;

import com.cet.electric.baseconfig.sdk.entity.Tree;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.Result;
import com.cet.electric.powercloud.common.model.device.QueryCommonTreeRequest;
import com.cet.electric.powercloud.overview.core.service.NodeTreeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Api(value = "/powercloud/overview/api/nodeTree", tags = "节点树查询接口")
@RestController
@Slf4j
@RequestMapping(value = "/powercloud/overview/api/nodeTree")
public class NodeTreeManageApi {

    @Value("${businessAuth.secondaryAuth:true}")
    private Boolean secondaryAuth;

    @Autowired
    private BusinessAuthService businessAuthService;

    @Autowired
    private NodeTreeService nodeTreeService;

//    @ApiOperation(value = "通用节点树-增强版，优先使用这个接口")
//    @PostMapping(value = "/common")
//    public Result<List<HierarchyNodeTreeVo>> commonNodeTree(@RequestBody QueryCommonNodeTreeRequest queryCommonNodeTreeRequest) {
//        ParamsAssert.notNull(queryCommonNodeTreeRequest);
//        ParamsAssert.notBlank(queryCommonNodeTreeRequest.getRootLabel());
//        ParamsAssert.notNull(queryCommonNodeTreeRequest.getRootId());
//        if (Objects.isNull(queryCommonNodeTreeRequest.getByAuth())) {
//            queryCommonNodeTreeRequest.setByAuth(Boolean.TRUE);
//        }
//        if (CollUtil.isEmpty(queryCommonNodeTreeRequest.getSubLabelList())) {
//            queryCommonNodeTreeRequest.setSubLabelList(Collections.emptyList());
//        }
//        if (Objects.isNull(queryCommonNodeTreeRequest.getTheUserId())) {
//            FusionUserDTO fusionUserDTO =businessAuthService.getUser();
//            UserVo user =new UserVo();
//            ConvertUtil.copyProperties(fusionUserDTO,user);
//            queryCommonNodeTreeRequest.setTheUserId(user.getId());
//        }
//        QueryCommonNodeTreeParams queryCommonNodeTreeParams = new QueryCommonNodeTreeParams();
//        ConvertUtil.copyProperties(queryCommonNodeTreeRequest, queryCommonNodeTreeParams);
//        queryCommonNodeTreeParams.setSecondaryAuth(secondaryAuth);
//        return Result.success(nodeTreeService.commonNodeTree(queryCommonNodeTreeParams));
//    }

    @ApiOperation(value = "通用节点树-增强版，优先使用这个接口")
    @PostMapping(value = "/common")
    public Result<List<Tree>> commonNodeTree(@RequestBody QueryCommonTreeRequest queryCommonNodeTreeRequest) {
        return Result.success(nodeTreeService.commonNodeTreeOverview(queryCommonNodeTreeRequest));
    }
}
