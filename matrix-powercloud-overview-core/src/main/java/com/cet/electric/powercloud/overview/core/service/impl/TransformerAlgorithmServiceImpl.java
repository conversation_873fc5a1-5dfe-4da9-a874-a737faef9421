package com.cet.electric.powercloud.overview.core.service.impl;

import com.cet.electric.powercloud.common.utils.CommonUtil;
import com.cet.electric.powercloud.overview.common.entity.PowerTransformer;
import com.cet.electric.powercloud.overview.common.enums.TransformerCoilTypeEnum;
import com.cet.electric.powercloud.overview.core.service.TransformerAlgorithmService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class TransformerAlgorithmServiceImpl implements TransformerAlgorithmService {
    @Override
    public Double getRatedCapacity(PowerTransformer transformer) {
        if (Objects.isNull(transformer) || Objects.isNull(transformer.getCoilType())) {
            return null;
        }
        switch (TransformerCoilTypeEnum.getByType(transformer.getCoilType())) {
            case DUAL_WINDING:
                return transformer.getRatedCapacity();
            case TRIPLE_WINDING:
                return CommonUtil.calculateDoublesMax(Arrays.asList(transformer.getRatedCapacityOfPrimary(),
                        transformer.getRatedCapacityOfSecondary(),
                        transformer.getRatedCapacityOfTertiary()));
            default:
                return null;
        }
    }
}
