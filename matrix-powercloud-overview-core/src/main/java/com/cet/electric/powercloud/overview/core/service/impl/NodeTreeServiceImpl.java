package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.cet.electric.baseconfig.sdk.entity.Tree;
import com.cet.electric.baseconfig.sdk.entity.dto.LabelAndIdAndName;
import com.cet.electric.baseconfig.sdk.entity.param.LabelAndId;
import com.cet.electric.baseconfig.sdk.service.TreeService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.modelservice.common.constant.ModelOperatorEnum;
import com.cet.electric.modelservice.common.query.*;
import com.cet.electric.modelservice.core.query.impl.ModelExtendService;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.modelservice.sdk.exceptions.ModelServiceException;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.enums.device.DeviceLanguageEnum;
import com.cet.electric.powercloud.common.exception.BusinessException;
import com.cet.electric.powercloud.common.matrix.BaseConfigDeviceService;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.auth.ModelNode;
import com.cet.electric.powercloud.common.model.device.DeviceModel;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.common.model.device.HierarchyNodeTreeVo;
import com.cet.electric.powercloud.common.model.device.QueryCommonTreeRequest;
import com.cet.electric.powercloud.common.utils.AuthUtil;
import com.cet.electric.powercloud.overview.common.constants.ModelConstant;
import com.cet.electric.powercloud.overview.common.constants.SymbolConstant;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.entity.QueryCommonNodeTreeParams;
import com.cet.electric.powercloud.overview.common.entity.WisdomRobot;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.core.mapper.WisdomRobotMapper;
import com.cet.electric.powercloud.overview.core.service.DeviceService;
import com.cet.electric.powercloud.overview.core.service.NodeTreeService;
import com.cet.electric.powercloud.overview.core.service.WisdomService;
import com.cet.futureblue.i18n.LanguageUtil;
import com.cet.powercloud.wisdom.feign.spring.boot.model.QueryCameraParam;
import com.cet.powercloud.wisdom.feign.spring.boot.model.WisdomCamera;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class NodeTreeServiceImpl implements NodeTreeService {
    @Resource
    BaseConfigDeviceService baseConfigDeviceService;


    @Autowired
    private BusinessAuthService businessAuthService;

    @Autowired
    private ModelExtendService modelExtendService;

    @Resource
    WisdomService wisdomService;
    @Resource
    WisdomRobotMapper wisdomRobotMapper;
    @Resource
    DeviceService deviceService;
    @Resource
    TreeService treeService;


    private final List<String> propList = Arrays.asList(ModelConstant.ID, ModelConstant.NAME, ModelConstant.ROOM_TYPE);

    @Override
    public List<HierarchyNodeTreeVo> commonNodeTree(QueryCommonNodeTreeParams queryCommonNodeTreeParams) {
        Boolean byAuth = queryCommonNodeTreeParams.getByAuth();
        Long rootId = queryCommonNodeTreeParams.getRootId();
        String rootLabel = queryCommonNodeTreeParams.getRootLabel();
        List<String> subLabelList = Objects.isNull(queryCommonNodeTreeParams.getSubLabelList()) ? Collections.emptyList() : queryCommonNodeTreeParams.getSubLabelList();
        Integer energyType = queryCommonNodeTreeParams.getEnergyType();
        ParamsAssert.notNull(byAuth);
        ParamsAssert.notBlank(rootLabel);
        ParamsAssert.notNull(rootId);
        ModelQuery modelQuery = new ModelQuery(rootLabel, rootId);
        modelQuery.setTreeReturnEnable(Boolean.TRUE);
        RootCondition rootCondition = new RootCondition();
        rootCondition.setProps(propList);
        Filter rootFilter = new Filter();
        // 得到需要鉴权的节点类型
        boolean isSystemManager = businessAuthService.isSystemManager(queryCommonNodeTreeParams.getTheUserId());
        Map<String, List<Long>> authLabelToIdListMap = getAuthLabelToIdListMap(queryCommonNodeTreeParams, rootLabel, subLabelList, byAuth);
        if (Boolean.TRUE.equals(byAuth) && TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(rootLabel)) {
            // 非ROOT或者系统管理员，查询空值会查出所有的值，主动赋值-1
            if (CollUtil.isEmpty(authLabelToIdListMap.get(rootLabel)) && !isSystemManager) {
                authLabelToIdListMap.put(rootLabel, Collections.singletonList(-1L));
            }
            rootFilter.expression(getIdInExpression(authLabelToIdListMap.get(rootLabel)));
        }
        List<Long> rootIds = queryCommonNodeTreeParams.getRootIds();
        if (CollUtil.isNotEmpty(rootIds)) {
            rootFilter.expression(getIdInExpression(rootIds));
        }
        if (Objects.equals(rootLabel, TableNameConstant.ROOM) && CollUtil.isNotEmpty(queryCommonNodeTreeParams.getRoomTypes())) {
            rootFilter.expression(getRootTypeInExpression(queryCommonNodeTreeParams.getRoomTypes()));
        }
        rootCondition.setFilter(rootFilter);
        List<SubLayerCondition> subLayerConditionList = new ArrayList<>();
        for (String subLabel : subLabelList) {
            SubLayerCondition subLayerCondition = getSubLayerCondition(subLabel);
            subLayerConditionList.add(subLayerCondition);
            if (Boolean.TRUE.equals(byAuth) && TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(subLabel)) {
                // 非ROOT或者系统管理员，查询空值会查出所有的值，主动赋值-1
                if (CollUtil.isEmpty(authLabelToIdListMap.get(subLabel)) && !isSystemManager) {
                    authLabelToIdListMap.put(subLabel, Collections.singletonList(-1L));
                }
                Filter filter = getIdInFilter(authLabelToIdListMap.get(subLabel));
                if (Objects.equals(subLabel, TableNameConstant.ROOM) && CollUtil.isNotEmpty(queryCommonNodeTreeParams.getRoomTypes())) {
                    filter.expression(getRootTypeInExpression(queryCommonNodeTreeParams.getRoomTypes()));
                }
                if (Objects.equals(subLabel, TableNameConstant.PIPE_LINE) && Objects.nonNull(energyType) && energyType != 0) {
                    Expression express = new Expression();
                    express.setProp(ModelConstant.ENERGY_TYPE);
                    express.setOperator(ModelOperatorEnum.EQ.name());
                    express.setLimit(energyType);
                    filter.expression(express);
                    subLayerCondition.filter(filter);
                }
                subLayerCondition.filter(filter);
            }
        }
        modelQuery.setSubLayerConditions(subLayerConditionList);
        modelQuery.setRootCondition(rootCondition);
        ApiResult<List<HierarchyNodeTreeVo>> listApiResult = modelExtendService.queryModelData(modelQuery, HierarchyNodeTreeVo.class);
        checkResult(listApiResult);
        filterLineSegmentWithSwitch(listApiResult.getData());
        List<EquipmentAccount> equipmentAccounts = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.PROJECT, Arrays.asList(0L), 1L, 1L);
        if (CollUtil.isEmpty(equipmentAccounts)) {
            return listApiResult.getData();
        }
        Map<ModelInfo, EquipmentAccount> sortMap = equipmentAccounts.stream()
                .collect(Collectors.toMap(
                        e -> new ModelInfo(e.getId(), e.getModelLabel()),
                        e -> e,
                        (e1, e2) -> e1));
        return sortDevice(listApiResult.getData(), sortMap);
    }

    private void filterLineSegmentWithSwitch(List<HierarchyNodeTreeVo> nodeTrees) {
        if (CollUtil.isEmpty(nodeTrees)) {
            return;
        }
        for (HierarchyNodeTreeVo it : nodeTrees) {
            if (ObjectUtils.notEqual(it.getModelLabel(), TableNameConstant.PROJECT)) {
                filterLineSegmentWithSwitch(it.getChildren());
            }
            if (Objects.equals(it.getModelLabel(), TableNameConstant.PROJECT) && CollUtil.isNotEmpty(it.getChildren())) {
                it.getChildren().removeIf(child -> Objects.equals(child.getModelLabel(), TableNameConstant.LINE_SEGMENT_WITH_SWITCH));
            }
        }
    }

    private List<HierarchyNodeTreeVo> sortDevice(List<HierarchyNodeTreeVo> nodeTree, Map<ModelInfo, EquipmentAccount> sortMap) {
        if (CollUtil.isEmpty(nodeTree)) {
            return Collections.emptyList();
        }
        if (MapUtil.isEmpty(sortMap)) {
            Set<Long> roomIdSet = nodeTree.stream()
                    .filter(item -> Objects.equals(item.getModelLabel(), TableNameConstant.ROOM) && Objects.nonNull(item.getId()))
                    .map(HierarchyNodeTreeVo::getId)
                    .collect(Collectors.toSet());
            if (CollUtil.isEmpty(roomIdSet)) {
                return nodeTree;
            }
            List<EquipmentAccount> equipmentAccounts = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.ROOM, new ArrayList<>(roomIdSet));

            if (CollUtil.isEmpty(equipmentAccounts)) {
                return nodeTree;
            }
            sortMap = equipmentAccounts.stream()
                    .collect(Collectors.toMap(
                            e -> new ModelInfo(e.getId(), e.getModelLabel()),
                            e -> e,
                            (e1, e2) -> e1));
        }
        for (HierarchyNodeTreeVo hierarchyNodeTreeVo : nodeTree) {
            if (CollUtil.isEmpty(hierarchyNodeTreeVo.getChildren())) {
                continue;
            }
            if (Objects.equals(hierarchyNodeTreeVo.getModelLabel(), TableNameConstant.PROJECT)
                    || Objects.equals(hierarchyNodeTreeVo.getModelLabel(), TableNameConstant.BUILDING)
                    || Objects.equals(hierarchyNodeTreeVo.getModelLabel(), TableNameConstant.FLOOR)) {
                hierarchyNodeTreeVo.getChildren().sort(Comparator.comparingLong(HierarchyNodeTreeVo::getId));
            }
            if (Objects.equals(hierarchyNodeTreeVo.getModelLabel(), TableNameConstant.ROOM)
                    || Objects.equals(hierarchyNodeTreeVo.getModelLabel(), TableNameConstant.POWER_DIS_CABINET)
                    || Objects.equals(hierarchyNodeTreeVo.getModelLabel(), TableNameConstant.PV_ENERGY_CONTAINER)) {
                Map<ModelInfo, EquipmentAccount> finalSortMap = sortMap;
                hierarchyNodeTreeVo.getChildren().sort(Comparator.comparing(
                        c -> finalSortMap.get(new ModelInfo(c.getId(), c.getModelLabel())),
                        Comparator.nullsLast(Comparator.comparingLong((EquipmentAccount e) ->
                                e.getId() != null ? e.getId() : Long.MAX_VALUE
                        ).thenComparingLong(EquipmentAccount::getId))));
            }
            sortDevice(hierarchyNodeTreeVo.getChildren(), sortMap);
        }
        return nodeTree;
    }

    /**
     * 校验模型服务结果
     *
     * @param listApiResult 模型服务结果
     */
    private void checkResult(ApiResult<?> listApiResult) {
        if (Boolean.FALSE.equals(listApiResult.isSuccess())) {
            throw new BusinessException(LanguageUtil.getMessage(DeviceLanguageEnum.ERROR_DEVICE_QUERY_MODEL.getMsg()) + listApiResult.getMsg());
        }
    }

    /**
     * 生成id过滤条件
     *
     * @param ids ids
     * @return Filter
     */
    private Filter getIdInFilter(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new Filter();
        }
        Filter filter = new Filter();
        Expression projectExpression = getIdInExpression(ids);
        filter.expression(projectExpression);
        return filter;
    }

    /**
     * 生成子层级查询条件
     *
     * @param modelLabel modelLabel
     * @return SubLayerCondition
     */
    private SubLayerCondition getSubLayerCondition(String modelLabel) {
        SubLayerCondition subLayerCondition = new SubLayerCondition();
        subLayerCondition.setProps(propList);
        subLayerCondition.setModelLabel(modelLabel);
        return subLayerCondition;
    }


    private Expression getRootTypeInExpression(List<Integer> roomTypes) {
        Expression expression = new Expression();
        expression.setProp(ModelConstant.ROOM_TYPE);
        expression.setOperator(ModelOperatorEnum.IN.name());
        expression.setLimit(roomTypes);
        return expression;
    }

    private Expression getIdInExpression(List<Long> ids) {
        Expression expression = new Expression();
        expression.setProp(ModelConstant.ID);
        expression.setOperator(ModelOperatorEnum.IN.name());
        expression.setLimit(ids);
        return expression;
    }

    private Map<String, List<Long>> getAuthLabelToIdListMap(QueryCommonNodeTreeParams queryCommonNodeTreeParams, String rootLabel, List<String> subLabelList, Boolean byAuth) {
        Set<String> labelSet = new HashSet<>();
        labelSet.add(rootLabel);
        labelSet.addAll(subLabelList);
        // 不是所有节点都有权限控制，4.0项目楼栋楼层房间可以权限控制
        Set<String> authLabelSet = labelSet.stream().filter(TableNameConstant.REQUIRE_AUTHENTICATION_MODELS::contains).collect(Collectors.toSet());
        Map<String, List<Long>> authLabelToIdListMap = Collections.emptyMap();
        // 开始鉴权
        if (Boolean.TRUE.equals(byAuth) && CollUtil.isNotEmpty(authLabelSet)) {
            Long userId = queryCommonNodeTreeParams.getTheUserId();

            if (Objects.isNull(userId)) {
                userId = businessAuthService.getUserId();
            }
            // 查出需要鉴权的节点
            authLabelToIdListMap = businessAuthService.getAuthNodes(AuthModelNodesEnum.getEnumList(authLabelSet), userId)
                    .stream()
                    .collect(
                            Collectors.groupingBy(ModelNode::getModelLabel,
                                    Collectors.mapping(ModelNode::getId,
                                            Collectors.toList())));
            // 二次过滤开启条件
            // 1.配置项开启
            // 2.要查询的用户与登录用户不一致
            // 3.登录用户不是系统管理员或者ROOT
            Long loginUserId = businessAuthService.getUserId();
            if (Boolean.TRUE.equals(queryCommonNodeTreeParams.getSecondaryAuth())
                    && !Objects.equals(userId, loginUserId)
                    && !businessAuthService.isSystemManager()) {
                List<ModelNode> authNodes = businessAuthService.getAuthNodes(AuthModelNodesEnum.getEnumList(authLabelSet), loginUserId);
                // 要查询的是系统管理员角色或者ROOT,直接把登录角色权限返回
                if (businessAuthService.isSystemManager(userId)) {
                    return authNodes
                            .stream()
                            .collect(
                                    Collectors.groupingBy(ModelNode::getModelLabel,
                                            Collectors.mapping(ModelNode::getId,
                                                    Collectors.toList())));
                }
                Map<String, Set<Long>> secondaryAuthLabelToIdListMap = authNodes
                        .stream()
                        .collect(
                                Collectors.groupingBy(ModelNode::getModelLabel,
                                        Collectors.mapping(ModelNode::getId,
                                                Collectors.toSet())));
                for (Map.Entry<String, List<Long>> entry : authLabelToIdListMap.entrySet()) {
                    String authLabel = entry.getKey();
                    List<Long> authIds = entry.getValue();
                    // 移除登录用户中不存在的权限
                    for (int i = authIds.size() - 1; i >= 0; i--) {
                        Long authId = authIds.get(i);
                        Set<Long> secondaryAuthIdSet = secondaryAuthLabelToIdListMap.getOrDefault(authLabel, Collections.emptySet());
                        if (!secondaryAuthIdSet.contains(authId)) {
                            authIds.remove(i);
                        }
                    }
                }
            }
        }
        return authLabelToIdListMap;
    }

    @Override
    public List<Tree> commonNodeTreeOverview(QueryCommonTreeRequest queryCommonNodeTreeRequest) {
        List<Tree> trees = baseConfigDeviceService.queryCommonNodeTree(AuthUtil.getTenantId(), AuthUtil.getUserId(), queryCommonNodeTreeRequest);
        if (queryCommonNodeTreeRequest.getSubLabelList().contains(ModelConstant.WISDOM_CAMERAS)) {
            cameraTreeHandler(trees);
        }
        if (queryCommonNodeTreeRequest.getSubLabelList().contains(ModelConstant.WISDOM_ROBOT)) {
            List<WisdomRobot> robotList;
            try {
                robotList = new LambdaQueryChainWrapper<>(wisdomRobotMapper).list();
            } catch (ModelServiceException e) {
                robotList = Collections.emptyList();
            }
            if (CollUtil.isNotEmpty(robotList)) {
                robotTreeHandler(trees, robotList);
            }
        }
        emptyChildren(trees);
        return trees;
    }


    @Override
    public List<EquipmentAccount> getEquipmentAccounts(String modelLabel, List<Long> modelIds) {
        return getEquipmentAccounts(modelLabel, modelIds, AuthUtil.getTenantId(), AuthUtil.getUserId());
    }

    @Override
    public List<EquipmentAccount> getEquipmentAccounts(String modelLabel, List<Long> modelIds, Long tenantId, Long userId) {
        if (Objects.equals(ModelConstant.WISDOM_CAMERAS, modelLabel)) {
            return getEquipmentAccountsByCameraIds(modelIds);
        } else if (Objects.equals(ModelConstant.WISDOM_ROBOT, modelLabel)) {
            return getEquipmentAccountsByRobotIds(modelIds);
        } else {
            List<EquipmentAccount> equipmentAccounts = baseConfigDeviceService.getEquipmentAccounts(modelLabel, modelIds, tenantId, userId);
            if (TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(modelLabel)) {
                List<Long> authRoomIds = deviceService.underLabelRooms(modelLabel, modelIds, Collections.emptyList())
                        .stream()
                        .map(Tree::getId)
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(authRoomIds)) {
                    return equipmentAccounts;
                }
                if (CollUtil.isEmpty(equipmentAccounts)) {
                    equipmentAccounts = new ArrayList<>();
                }
                // 补充摄像头
                fillCameraEquipmentAccount(authRoomIds, equipmentAccounts);
                // 补充机器人
                fillRobotEquipmentAccount(authRoomIds, equipmentAccounts);
            }
            return equipmentAccounts;
        }
    }

    @Override
    public List<EquipmentAccount> getEquipmentAccounts(List<LabelAndId> labelAndIds) {
        List<LabelAndId> pipelineLabelAndIds = labelAndIds.stream()
                .filter(labelAndId -> !Objects.equals(ModelConstant.WISDOM_CAMERAS, labelAndId.getModelLabel())
                        && !Objects.equals(ModelConstant.WISDOM_ROBOT, labelAndId.getModelLabel()))
                .collect(Collectors.toList());
        List<EquipmentAccount> equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(pipelineLabelAndIds);
        if (CollUtil.isEmpty(equipmentAccountList)) {
            equipmentAccountList = new ArrayList<>();
        }
        // 补充摄像机
        List<Long> cameraIds = labelAndIds.stream()
                .filter(labelAndId -> Objects.equals(ModelConstant.WISDOM_CAMERAS, labelAndId.getModelLabel()))
                .map(LabelAndId::getId)
                .collect(Collectors.toList());
        equipmentAccountList.addAll(getEquipmentAccountsByCameraIds(cameraIds));
        // 补充机器人
        List<Long> robotIds = labelAndIds.stream()
                .filter(labelAndId -> Objects.equals(ModelConstant.WISDOM_ROBOT, labelAndId.getModelLabel()))
                .map(LabelAndId::getId)
                .collect(Collectors.toList());
        equipmentAccountList.addAll(getEquipmentAccountsByRobotIds(robotIds));
        return equipmentAccountList;
    }

    @Override
    public List<DeviceModel> getDeviceNodes(String modelLabel, List<Long> modelIds) {
        if (TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(modelLabel)) {
            List<Long> authRoomIds = deviceService.underLabelRooms(modelLabel, modelIds, Collections.emptyList())
                    .stream()
                    .map(Tree::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(authRoomIds)) {
                return Collections.emptyList();
            }
            List<DeviceModel> deviceNodes = baseConfigDeviceService.getDeviceNodes(modelLabel, modelIds);
            if (CollUtil.isEmpty(deviceNodes)) {
                deviceNodes = new ArrayList<>();
            }
            // 补充摄像头
            fillCameraDeviceModel(authRoomIds, deviceNodes);
            // 补充机器人
            fillRobotDeviceModel(authRoomIds, deviceNodes);
            return deviceNodes;
        } else if (Objects.equals(ModelConstant.WISDOM_CAMERAS, modelLabel)) {
            QueryCameraParam queryCameraParam = new QueryCameraParam();
            queryCameraParam.setCameraIds(modelIds);
            ApiResult<List<WisdomCamera>> listApiResult = wisdomService.queryCamera(queryCameraParam);
            List<WisdomCamera> cameras = listApiResult.getData();
            if (CollUtil.isEmpty(cameras)) {
                return Collections.emptyList();
            }
            return camerasConvertDeviceModels(cameras);
        } else if (Objects.equals(ModelConstant.WISDOM_ROBOT, modelLabel)) {
            List<WisdomRobot> robotList;
            try {
                robotList = new LambdaQueryChainWrapper<>(wisdomRobotMapper)
                        .in(WisdomRobot::getId, modelIds)
                        .list();
            } catch (ModelServiceException e) {
                robotList = Collections.emptyList();
            }
            if (CollUtil.isEmpty(robotList)) {
                return Collections.emptyList();
            }
            return robotsConvertDeviceModels(robotList);
        } else {
            return baseConfigDeviceService.getDeviceNodes(modelLabel, modelIds);
        }
    }

    private void fillRobotDeviceModel(List<Long> authRoomIds, List<DeviceModel> deviceNodes) {
        List<WisdomRobot> robotList;
        try {
            robotList = new LambdaQueryChainWrapper<>(wisdomRobotMapper)
                    .eq(WisdomRobot::getObjectLabel, com.cet.electric.powercloud.common.constant.TableNameConstant.ROOM)
                    .in(WisdomRobot::getObjectId, authRoomIds)
                    .list();
        } catch (ModelServiceException e) {
            robotList = Collections.emptyList();
        }
        if (CollUtil.isEmpty(robotList)) {
            return;
        }
        robotList = robotList.stream()
                .filter(robot -> authRoomIds.contains(robot.getObjectId()))
                .collect(Collectors.toList());
        List<DeviceModel> cameraDeviceModels = robotsConvertDeviceModels(robotList);
        deviceNodes.addAll(cameraDeviceModels);
    }

    private List<DeviceModel> robotsConvertDeviceModels(List<WisdomRobot> robotList) {
        return robotList.stream()
                .map(camera -> {
                    DeviceModel deviceModel = new DeviceModel();
                    deviceModel.setId(camera.getId());
                    deviceModel.setCode(camera.getCode());
                    deviceModel.setName(camera.getName());
                    deviceModel.setModelLabel(ModelConstant.WISDOM_ROBOT);
                    return deviceModel;
                }).collect(Collectors.toList());
    }

    private void fillCameraDeviceModel(List<Long> authRoomIds, List<DeviceModel> deviceNodes) {
        ApiResult<List<WisdomCamera>> listApiResult = wisdomService.queryCamera(new QueryCameraParam());
        List<WisdomCamera> cameras = listApiResult.getData();
        if (CollUtil.isEmpty(cameras)) {
            return;
        }
        cameras = cameras.stream()
                .filter(camera -> authRoomIds.contains(camera.getObjectId()))
                .collect(Collectors.toList());
        List<DeviceModel> cameraDeviceModels = camerasConvertDeviceModels(cameras);
        deviceNodes.addAll(cameraDeviceModels);
    }

    private List<DeviceModel> camerasConvertDeviceModels(List<WisdomCamera> cameras) {
        return cameras.stream()
                .map(camera -> {
                    DeviceModel deviceModel = new DeviceModel();
                    deviceModel.setId(camera.getId());
                    deviceModel.setCode(camera.getCode());
                    deviceModel.setName(camera.getName());
                    deviceModel.setModelLabel(ModelConstant.WISDOM_CAMERAS);
                    return deviceModel;
                }).collect(Collectors.toList());
    }

    private List<EquipmentAccount> getEquipmentAccountsByRobotIds(List<Long> modelIds) {
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }
        List<WisdomRobot> robotList;
        try {
            robotList = new LambdaQueryChainWrapper<>(wisdomRobotMapper)
                    .in(WisdomRobot::getId, modelIds)
                    .list();
        } catch (ModelServiceException e) {
            robotList = Collections.emptyList();
        }
        if (CollUtil.isEmpty(robotList)) {
            return Collections.emptyList();
        }
        return getEquipmentAccountsByRobots(robotList);
    }

    private List<EquipmentAccount> getEquipmentAccountsByRobots(List<WisdomRobot> robotList) {
        List<EquipmentAccount> equipmentAccounts = new ArrayList<>(robotList.size());
        List<LabelAndId> labelAndIds = robotList.stream()
                .map(item -> new LabelAndId(item.getObjectLabel(), item.getObjectId()))
                .distinct()
                .collect(Collectors.toList());
        // 查询设备的上行树结构
        List<Tree> upwardTreeList = treeService.upwardTree(labelAndIds, Tree.class);
        Map<WisdomRobot, Map<String, LabelAndIdAndName>> resultMap = new HashMap<>();
        robotList.forEach(robot -> {
            Map<String, LabelAndIdAndName> nodeMap = new HashMap<>();
            upwardTreeList.forEach(tree -> {
                if (Objects.equals(tree.getModelLabel(), robot.getObjectLabel()) && Objects.equals(tree.getId(), robot.getObjectId())) {
                    traverseHelper(tree, nodeMap);
                }
            });
            resultMap.put(robot, nodeMap);
        });
        for (Map.Entry<WisdomRobot, Map<String, LabelAndIdAndName>> entry : resultMap.entrySet()) {
            WisdomRobot robot = entry.getKey();
            Map<String, LabelAndIdAndName> nodeMap = entry.getValue();
            equipmentAccounts.add(getEquipmentAccount(ModelConstant.ROOT_LABEL, robot.getId(), robot.getName(), robot.getCode(), nodeMap));
        }
        return equipmentAccounts.stream()
                // 因为现在房间删了，房间机器人还在，所以需要将没有房间的机器人过滤掉
                .filter(equipmentAccount -> Objects.nonNull(equipmentAccount.getRoomId()))
                .collect(Collectors.toList());
    }

    private List<EquipmentAccount> getEquipmentAccountsByCameraIds(List<Long> modelIds) {
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }
        QueryCameraParam queryCameraParam = new QueryCameraParam();
        queryCameraParam.setCameraIds(modelIds);
        ApiResult<List<WisdomCamera>> listApiResult = wisdomService.queryCamera(queryCameraParam);
        List<WisdomCamera> cameras = listApiResult.getData();
        return getEquipmentAccountsByCameras(cameras);
    }

    private List<EquipmentAccount> getEquipmentAccountsByCameras(List<WisdomCamera> cameras) {
        if (CollUtil.isEmpty(cameras)) {
            return Collections.emptyList();
        }
        List<EquipmentAccount> equipmentAccounts = new ArrayList<>(cameras.size());
        List<LabelAndId> labelAndIds = cameras.stream()
                .map(item -> new LabelAndId(item.getObjectLabel(), item.getObjectId()))
                .distinct()
                .collect(Collectors.toList());
        // 查询设备的上行树结构
        List<Tree> upwardTreeList = treeService.upwardTree(labelAndIds, Tree.class);
        Map<WisdomCamera, Map<String, LabelAndIdAndName>> resultMap = new HashMap<>();
        cameras.forEach(camera -> {
            Map<String, LabelAndIdAndName> nodeMap = new HashMap<>();
            upwardTreeList.forEach(tree -> {
                if (Objects.equals(tree.getModelLabel(), camera.getObjectLabel()) && Objects.equals(tree.getId(), camera.getObjectId())) {
                    traverseHelper(tree, nodeMap);
                }
            });
            resultMap.put(camera, nodeMap);
        });
        for (Map.Entry<WisdomCamera, Map<String, LabelAndIdAndName>> entry : resultMap.entrySet()) {
            WisdomCamera camera = entry.getKey();
            Map<String, LabelAndIdAndName> nodeMap = entry.getValue();
            equipmentAccounts.add(getEquipmentAccount(ModelConstant.WISDOM_CAMERAS, camera.getId(), camera.getName(), camera.getCode(), nodeMap));
        }
        return equipmentAccounts.stream()
                // 因为现在房间删了，房间摄像头还在，所以需要将没有房间的摄像头过滤掉
                .filter(equipmentAccount -> Objects.nonNull(equipmentAccount.getRoomId()))
                .collect(Collectors.toList());
    }


    private void fillCameraEquipmentAccount(List<Long> authRoomIds, List<EquipmentAccount> equipmentAccounts) {
        ApiResult<List<WisdomCamera>> listApiResult = wisdomService.queryCamera(new QueryCameraParam());
        List<WisdomCamera> cameras = listApiResult.getData();
        equipmentAccounts.addAll(
                getEquipmentAccountsByCameras(cameras.stream()
                        .filter(camera -> authRoomIds.contains(camera.getObjectId()))
                        .collect(Collectors.toList())));
    }

    private void fillRobotEquipmentAccount(List<Long> authRoomIds, List<EquipmentAccount> equipmentAccounts) {
        List<WisdomRobot> robotList;
        try {
            robotList = new LambdaQueryChainWrapper<>(wisdomRobotMapper)
                    .eq(WisdomRobot::getObjectLabel, com.cet.electric.powercloud.common.constant.TableNameConstant.ROOM)
                    .in(WisdomRobot::getObjectId, authRoomIds)
                    .list();
        } catch (ModelServiceException e) {
            robotList = Collections.emptyList();
        }
        if (CollUtil.isNotEmpty(robotList)) {
            equipmentAccounts.addAll(getEquipmentAccountsByRobots(robotList));
        }
    }

    private EquipmentAccount getEquipmentAccount(String label,
                                                 Long id,
                                                 String name,
                                                 String code,
                                                 Map<String, LabelAndIdAndName> nodeMap) {
        EquipmentAccount newEquipmentAccount = new EquipmentAccount();
        newEquipmentAccount.setId(id);
        newEquipmentAccount.setModelLabel(label);
        newEquipmentAccount.setName(name);
        newEquipmentAccount.setCode(code);
        LabelAndIdAndName roomInfo = nodeMap.get(TableNameConstant.ROOM);
        LabelAndIdAndName floorInfo = nodeMap.get(TableNameConstant.FLOOR);
        LabelAndIdAndName buildingInfo = nodeMap.get(TableNameConstant.BUILDING);
        LabelAndIdAndName projectInfo = nodeMap.get(TableNameConstant.PROJECT);
        if (Objects.nonNull(roomInfo)) {
            newEquipmentAccount.setRoomId(roomInfo.getId());
            newEquipmentAccount.setRoomName(roomInfo.getName());
        }
        if (Objects.nonNull(floorInfo)) {
            newEquipmentAccount.setFloorId(floorInfo.getId());
            newEquipmentAccount.setFloorName(floorInfo.getName());
        }
        if (Objects.nonNull(buildingInfo)) {
            newEquipmentAccount.setBuildingId(buildingInfo.getId());
            newEquipmentAccount.setBuildingName(buildingInfo.getName());
        }
        if (Objects.nonNull(projectInfo)) {
            newEquipmentAccount.setProjectId(projectInfo.getId());
            newEquipmentAccount.setProjectName(projectInfo.getName());
        }
        return newEquipmentAccount;
    }

    private void cameraTreeHandler(List<Tree> hierarchyNodeTreeList) {
        ApiResult<List<WisdomCamera>> listApiResult = wisdomService.queryCamera(new QueryCameraParam());
        if (CollUtil.isEmpty(listApiResult.getData())) {
            return;
        }
        // 将摄像头放在对应的层级下
        parseCameras(listApiResult.getData(), hierarchyNodeTreeList);
    }

    private void parseCameras(List<WisdomCamera> cameraList, List<Tree> hierarchyNodeTreeList) {
        cameraList.forEach(camera -> assignCameraToNode(hierarchyNodeTreeList, camera));
    }

    private boolean assignCameraToNode(List<Tree> nodeList, WisdomCamera camera) {
        for (Tree node : nodeList) {
            // 如果当前节点匹配摄像头
            if (Objects.equals(node.getModelLabel(), camera.getObjectLabel())
                    && Objects.equals(node.getId(), camera.getObjectId())) {
                // 将摄像头添加到该节点的摄像头列表中
                if (CollUtil.isEmpty(node.getChildren())) {
                    node.setChildren(new ArrayList<>());
                }
                node.getChildren().add(toHierarchyNodeTree(camera));
                return true;
            }

            // 如果当前节点有子节点，递归遍历
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                boolean found = assignCameraToNode(node.getChildren(), camera);
                if (found) {
                    return true;
                }
            }
        }
        return false;
    }

    private Tree toHierarchyNodeTree(WisdomCamera camera) {
        Tree nodeTreeVo = new Tree();
        nodeTreeVo.setName(camera.getName());
        nodeTreeVo.setId(camera.getId());
        nodeTreeVo.setModelLabel(ModelConstant.WISDOM_CAMERAS);
        nodeTreeVo.setTreeId(ModelConstant.WISDOM_CAMERAS + "_" + camera.getId());
        return nodeTreeVo;
    }

    private void robotTreeHandler(List<Tree> hierarchyNodeTreeList, List<WisdomRobot> robotList) {
        if (CollUtil.isEmpty(hierarchyNodeTreeList)) {
            return;
        }
        for (Tree tree : hierarchyNodeTreeList) {
            if (CollUtil.isNotEmpty(tree.getChildren())) {
                robotTreeHandler(tree.getChildren(), robotList);
            } else {
                tree.setChildren(new ArrayList<>());
            }
            tree.getChildren().addAll(
                    robotList.stream()
                            .filter(r -> Objects.equals(r.getObjectId(), tree.getId()) && Objects.equals(r.getObjectLabel(), tree.getModelLabel()))
                            .map(r -> new Tree(r.getId(), ModelConstant.WISDOM_ROBOT, r.getName(), ModelConstant.WISDOM_ROBOT + SymbolConstant.UNDER_SCORE + r.getId(), null))
                            .collect(Collectors.toList()));
        }
    }

    private void emptyChildren(List<Tree> trees) {
        // 如果children是空数据，就设置为null
        trees.forEach(tree -> {
            if (CollUtil.isEmpty(tree.getChildren())) {
                tree.setChildren(null);
            } else {
                emptyChildren(tree.getChildren());
            }
        });
    }

    private void traverseHelper(Tree tree, Map<String, LabelAndIdAndName> nodeMap) {
        if (Objects.isNull(tree)) {
            return;
        }
        nodeMap.put(tree.getModelLabel(), new LabelAndIdAndName(tree.getModelLabel(), tree.getId(), tree.getName()));
        if (CollUtil.isNotEmpty(tree.getChildren())) {
            for (Tree child : tree.getChildren()) {
                traverseHelper(child, nodeMap); // 递归处理每个子节点
            }
        }
    }
}
