package com.cet.electric.powercloud.overview.core.service.impl;

import com.cet.electric.baseconfig.sdk.common.utils.JsonTransferUtils;
import com.cet.electric.powercloud.common.enums.device.RoomTypeEnum;
import com.cet.electric.powercloud.overview.common.model.dto.EnergyStorageRoomExtend;
import com.cet.electric.powercloud.overview.common.model.dto.PvStationRoomExtend;
import com.cet.electric.powercloud.overview.common.model.dto.WindFarmRoomExtend;
import com.cet.electric.powercloud.overview.common.utils.DoubleUtil;
import com.cet.electric.powercloud.overview.core.service.PhotovoltaicAlgorithmService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class PhotovoltaicAlgorithmServiceImpl implements PhotovoltaicAlgorithmService {
    @Override
    public Double getRoomCapacity(int roomType, String extend) {
        if (Objects.isNull(extend)) {
            return 0d;
        }
        if (roomType == RoomTypeEnum.PHOTOVOLTAIC_STATION.getCode()) {
            return JsonTransferUtils.parseString(extend, PvStationRoomExtend.class)
                    .getTotalInstalledCapacity();
        } else if (roomType == RoomTypeEnum.ENERGY_STORAGE_STATION.getCode()) {
            return JsonTransferUtils.parseString(extend, EnergyStorageRoomExtend.class)
                    .getTotalInstalledCapacity();
        } else if (roomType == RoomTypeEnum.WIND_POWER_PLANT.getCode()) {
            return JsonTransferUtils.parseString(extend, WindFarmRoomExtend.class)
                    .getTotalInstalledCapacity();
        } else {
            return 0d;
        }
    }

    @Override
    public Double getRoomGirdVoltage(int roomType, String extend) {
        if (Objects.isNull(extend)) {
            return 0d;
        }
        if (roomType == RoomTypeEnum.PHOTOVOLTAIC_STATION.getCode()) {
            return JsonTransferUtils.parseString(extend, PvStationRoomExtend.class)
                    .getGirdVoltage();
        } else if (roomType == RoomTypeEnum.ENERGY_STORAGE_STATION.getCode()) {
            return JsonTransferUtils.parseString(extend, EnergyStorageRoomExtend.class)
                    .getGirdVoltage();
        } else if (roomType == RoomTypeEnum.WIND_POWER_PLANT.getCode()) {
            return JsonTransferUtils.parseString(extend, WindFarmRoomExtend.class)
                    .getGirdVoltage();
        } else {
            return 0d;
        }
    }

    @Override
    public Double getRoomRatedPower(int roomType, String extend) {
        if (Objects.isNull(extend)) {
            return 0d;
        }
        if (roomType == RoomTypeEnum.PHOTOVOLTAIC_STATION.getCode()) {
            return JsonTransferUtils.parseString(extend, PvStationRoomExtend.class)
                    .getTotalRatedPower();
        } else if (roomType == RoomTypeEnum.ENERGY_STORAGE_STATION.getCode()) {
            return JsonTransferUtils.parseString(extend, EnergyStorageRoomExtend.class)
                    .getTotalRatedPower();
        } else if (roomType == RoomTypeEnum.WIND_POWER_PLANT.getCode()) {
            return JsonTransferUtils.parseString(extend, WindFarmRoomExtend.class)
                    .getTotalRatedPower();
        } else {
            return 0d;
        }
    }

    @Override
    public Double calculateEquivalentOutputHour(Double output, Double ratedPower) {

        if (Objects.isNull(output) || Objects.isNull(ratedPower)) {
            return null;
        }
        if (!DoubleUtil.isValidDenominator(ratedPower)) {
            return null;
        }
        return output / ratedPower;
    }
}
