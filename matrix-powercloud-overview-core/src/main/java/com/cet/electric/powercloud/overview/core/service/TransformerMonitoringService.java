package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.MeasurePointData;
import com.cet.electric.powercloud.overview.common.model.dto.PecRealTimeDataDTO;

import java.util.List;
import java.util.Map;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
public interface TransformerMonitoringService {
    /**
     * @param ids     变压器idList
     * @param dataIds 测点idList
     * @return 返回设备与实时数据映射map
     */
    Map<String, MeasurePointData> getRealDataToDeviceMap(List<Long> ids, List<Long> dataIds);

    /**
     * @param ids              变压器ids
     * @param apparentPowerMap 总视在功率map
     * @return 负载率值返回
     */
    Double queryLoadFactor(List<Long> ids, Map<Long, Double> apparentPowerMap);
}
