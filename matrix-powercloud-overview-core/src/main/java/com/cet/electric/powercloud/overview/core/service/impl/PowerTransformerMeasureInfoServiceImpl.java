package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.baseconfig.common.entity.MeasuredBy;
import com.cet.electric.baseconfig.common.entity.PipeNetworkConnectionModel;
import com.cet.electric.powercloud.common.enums.common.FlowTypeEnum;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.model.dto.PowerTransformerMeasureBy;
import com.cet.electric.powercloud.overview.core.service.DeviceRelationService;
import com.cet.electric.powercloud.overview.core.service.PipeNetworkConnectionService;
import com.cet.electric.powercloud.overview.core.service.PowerTransformerMeasureInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class PowerTransformerMeasureInfoServiceImpl implements PowerTransformerMeasureInfoService {
    @Autowired
    private PipeNetworkConnectionService pipeNetworkConnectionService;

    @Autowired
    private DeviceRelationService deviceRelationService;

    @Override
    public List<PowerTransformerMeasureBy> getPowerTransformerPecDeviceIdBatch(List<Long> powerTransformerIds) {
        if (CollUtil.isEmpty(powerTransformerIds)) {
            return Collections.emptyList();
        }
        List<ModelInfo> modelList = powerTransformerIds.stream()
                .map(id -> new ModelInfo(id, TableNameConstant.POWER_TRANSFORMER))
                .collect(Collectors.toList());
        List<PipeNetworkConnectionModel> inFlowModels = pipeNetworkConnectionService.queryPipeNetworkConnectBatch(modelList, FlowTypeEnum.INFLOW.getId());

        List<PipeNetworkConnectionModel> outFlowModels = pipeNetworkConnectionService.queryPipeNetworkConnectBatch(modelList, FlowTypeEnum.OUTFLOW.getId());

        List<ModelInfo> inModelList = inFlowModels.stream()
                .map(in -> new ModelInfo(in.getInflowId(), in.getInflowLabel()))
                .collect(Collectors.toList());
        modelList.addAll(inModelList);
        List<ModelInfo> outModelList = outFlowModels.stream()
                .map(in -> new ModelInfo(in.getOutFlowId(), in.getOutFlowLabel()))
                .collect(Collectors.toList());
        modelList.addAll(outModelList);

        List<MeasuredBy> measuredByList = deviceRelationService.queryDeviceRelationBatch(modelList);
        Map<ModelInfo, Integer> deviceModelToPecIdMap = measuredByList.stream()
                .collect(Collectors.toMap(m -> new ModelInfo(m.getMonitoredId(), m.getMonitoredLabel()), MeasuredBy::getMeasuredBy,(k1,k2)->k2));
        Map<Long, List<PipeNetworkConnectionModel>> inflowMap = inFlowModels.stream()
                .collect(Collectors.groupingBy(PipeNetworkConnectionModel::getOutFlowId));
        Map<Long, List<PipeNetworkConnectionModel>> outFlowMap = outFlowModels.stream()
                .collect(Collectors.groupingBy(PipeNetworkConnectionModel::getInflowId));
        return powerTransformerIds.stream()
                .map(id -> {
                    PowerTransformerMeasureBy powerTransformerMeasureBy = new PowerTransformerMeasureBy();
                    powerTransformerMeasureBy.setPowerTransformerId(id);
                    List<PipeNetworkConnectionModel> inFlow = inflowMap.get(id);
                    if (CollUtil.isNotEmpty(inFlow)) {
                        PipeNetworkConnectionModel inFlowCon = inFlow.get(0);
                        ModelInfo inflowModel = new ModelInfo(inFlowCon.getInflowId(), inFlowCon.getInflowLabel());
                        if(deviceModelToPecIdMap.containsKey(inflowModel)){
                        powerTransformerMeasureBy.setInflowMeasureBy(deviceModelToPecIdMap.get(inflowModel).longValue());
                        powerTransformerMeasureBy.setInflowModel(inflowModel);
                        }
                    }
                    List<PipeNetworkConnectionModel> outFlow = outFlowMap.get(id);
                    if (CollUtil.isNotEmpty(outFlow)) {
                        PipeNetworkConnectionModel outFlowCon = outFlow.get(0);
                        ModelInfo outflowModel = new ModelInfo(outFlowCon.getOutFlowId(), outFlowCon.getOutFlowLabel());
                        if(deviceModelToPecIdMap.containsKey(outflowModel)){
                        powerTransformerMeasureBy.setOutflowMeasureBy(deviceModelToPecIdMap.get(outflowModel).longValue());
                        powerTransformerMeasureBy.setOutflowModel((outflowModel));
                        }
                    }
                    ModelInfo selfModel = new ModelInfo(id, TableNameConstant.POWER_TRANSFORMER);
                    if(deviceModelToPecIdMap.containsKey(selfModel)){
                        powerTransformerMeasureBy.setMeasureBy(deviceModelToPecIdMap.get(selfModel).longValue());
                    }
                    return powerTransformerMeasureBy;
                }).collect(Collectors.toList());
    }
}
