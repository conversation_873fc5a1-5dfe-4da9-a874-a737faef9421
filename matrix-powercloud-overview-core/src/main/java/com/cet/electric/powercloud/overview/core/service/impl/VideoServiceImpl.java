package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cet.electric.baseconfig.common.dao.NodeRelationShipMapper;
import com.cet.electric.baseconfig.common.entity.NodeRelationShip;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.client.VideoExtendService;
import com.cet.electric.powercloud.common.client.model.CamerasDTO;
import com.cet.electric.powercloud.common.constant.TableNameConstant;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.exception.BusinessException;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.device.Room;
import com.cet.electric.powercloud.common.utils.AuthUtil;
import com.cet.electric.powercloud.common.utils.Json;
import com.cet.electric.powercloud.overview.common.enums.ClarityEnum;
import com.cet.electric.powercloud.overview.common.enums.EnvironmentLanguageEnum;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.core.mapper.RoomMapper;
import com.cet.electric.powercloud.overview.core.service.RoomService;
import com.cet.electric.powercloud.overview.core.service.VideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class VideoServiceImpl implements VideoService {
    @Autowired
    private VideoManageFactory videoManageFactory;

    @Autowired
    private RoomService roomService;
    @Autowired
    private VideoExtendService videoExtendService;

    @Autowired
    private RoomMapper roomMapper;

    @Autowired
    private NodeRelationShipMapper relationShipMapper;

    @Autowired
    private BusinessAuthService businessAuthService;


    @Override
    public void controlsCamera(Long id, Integer controlType) {
        videoManageFactory.controlsCamera(id,controlType);
    }

    @Override
    public String getCameraUrl(Long id) {
        return videoManageFactory.getCameraUrl(id);
    }

    @Override
    public List<CamerasDTO> queryRoomCameras(Long roomId, Integer clarity) {

//        ParamsAssert.notNull(roomId);
//        Room room = roomService.queryRoomById(roomId);
//        if (Objects.isNull(room)) {
//            throw new BusinessException(EnvironmentLanguageEnum.ENVIRONMENT_ERROR_VIDEO_SERVICE_QUERYROOMCAMERAS);
//        }
//        if (Objects.isNull(room.getVideoLink())) {
//            return Collections.emptyList();
//        }
//        List<CamerasDTO> list = Json.parseToList(room.getVideoLink(), CamerasDTO.class);
//        if (CollUtil.isEmpty(list)) {
//            return Collections.emptyList();
//        }
//        List<Long> camerasIds = list.stream()
//                .map(CamerasDTO::getId)
//                .collect(Collectors.toList());
//        List<CamerasDTO> queryVideoResponses = videoExtendService.cameras().stream()
//                .filter(x -> camerasIds.contains(x.getId()))
//                .sorted(Comparator.comparing(CamerasDTO::getId))
//                .collect(Collectors.toList());
//        if (CollUtil.isEmpty(queryVideoResponses)) {
//            return Collections.emptyList();
//        }
//        for (CamerasDTO camerasDTO : queryVideoResponses) {
//            JSONObject playUrl = new JSONObject(camerasDTO.getPlayUrl());
//            if (Objects.isNull(clarity) || clarity == ClarityEnum.SD.getValue()) {
//                camerasDTO.setPlayUrl(playUrl.get("hls",String.class));
//            } else {
//                camerasDTO.setPlayUrl(playUrl.get("hlsHd",String.class));
//            }
//        }
//        return queryVideoResponses;

        List<NodeRelationShip> nodeRelationShipList = new LambdaQueryChainWrapper<>(relationShipMapper)
                .eq(NodeRelationShip::getNodeLabel, "video")
                .eq(NodeRelationShip::getObjectId, roomId)
                .eq(NodeRelationShip::getObjectLabel, TableNameConstant.ROOM)
                .list();
        if (CollUtil.isEmpty(nodeRelationShipList)) {
            return Collections.emptyList();
        }
        Set<Long> roomVideoIds = nodeRelationShipList.stream()
                .map(NodeRelationShip::getNodeId)
                .collect(Collectors.toSet());
        List<CamerasDTO> queryVideoResponses = videoExtendService.cameras();
        if (CollUtil.isEmpty(queryVideoResponses)) {
            return Collections.emptyList();
        }
        queryVideoResponses = queryVideoResponses.stream()
                .filter(v -> roomVideoIds.contains(v.getId()))
                .collect(Collectors.toList());
        if (!businessAuthService.isSystemManager()) {
            List<Long> authIds = businessAuthService.getAuthNodeIds(AuthModelNodesEnum.CAMERAS, AuthUtil.getUserId());
            queryVideoResponses = queryVideoResponses.stream()
                    .filter(v -> authIds.contains(v.getId()))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(queryVideoResponses)) {
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(queryVideoResponses)) {
            return Collections.emptyList();
        }
        for (CamerasDTO camerasDTO : queryVideoResponses) {
            JSONObject playUrl = JSON.parseObject(camerasDTO.getPlayUrl());
            if (Objects.isNull(clarity) || clarity == ClarityEnum.SD.getValue()) {
                camerasDTO.setPlayUrl(playUrl.getString("hls"));
            } else {
                camerasDTO.setPlayUrl(playUrl.getString("hlsHd"));
            }
        }
        return queryVideoResponses;
    }

}
