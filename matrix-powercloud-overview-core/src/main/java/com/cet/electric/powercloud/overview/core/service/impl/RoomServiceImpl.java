package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.PageParams;
import com.cet.electric.powercloud.common.model.device.Building;
import com.cet.electric.powercloud.common.model.device.Floor;
import com.cet.electric.powercloud.common.model.device.Project;
import com.cet.electric.powercloud.common.model.device.Room;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.common.model.dto.QueryRoomParams;
import com.cet.electric.powercloud.overview.core.mapper.RoomMapper;
import com.cet.electric.powercloud.overview.core.service.RoomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
public class RoomServiceImpl implements RoomService {

    @Autowired
    private RoomMapper roomMapper;

    @Autowired
    private BusinessAuthService businessAuthService;
    @Override
    public List<Room> queryAuthRoomByProjectIds(List<Long> authIds, String modelLabel) {
        LambdaQueryChainWrapper<Room> wrapper = new LambdaQueryChainWrapper<>(roomMapper);
        if (Objects.equals(TableNameConstant.PROJECT, modelLabel)) {
            wrapper.treeNode(Project.class, null, authIds);
        } else if (Objects.equals(TableNameConstant.BUILDING, modelLabel)) {
            wrapper.treeNode(Building.class, null, authIds);
        } else if (Objects.equals(TableNameConstant.FLOOR, modelLabel)) {
            wrapper.treeNode(Floor.class, null, authIds);
        }
        if (!businessAuthService.isSystemManager()) {
            List<Long> authRoomIds = businessAuthService.getAuthNodeIds(AuthModelNodesEnum.ROOM);
            wrapper.in(Room::getId, authRoomIds);
        }
        return wrapper.list();
    }

    @Override
    public Room queryRoomById(Long id) {
        return roomMapper.selectById(id);
    }

    @Override
    public ListWithTotal<Room> queryRoom(QueryRoomParams queryRoomParams) {
        PageParams pageParams = queryRoomParams.getPageParams();
        List<Long> ids = queryRoomParams.getIds();
        String name = queryRoomParams.getName();
        Long projectId = queryRoomParams.getProjectId();
        Integer roomType = queryRoomParams.getRoomType();
        List<Integer> roomTypes = queryRoomParams.getRoomTypes();
        LambdaQueryChainWrapper<Room> roomLambdaQueryChainWrapper = new LambdaQueryChainWrapper<>(roomMapper);
        roomLambdaQueryChainWrapper.orderByAsc(Room::getId);
        if (Objects.nonNull(projectId)) {
            businessAuthService.checkNodeAuth(AuthModelNodesEnum.PROJECT, projectId);
            roomLambdaQueryChainWrapper.treeNode(Project.class, projectId);
        }
        if (Objects.nonNull(roomType)) {
            roomLambdaQueryChainWrapper.eq(Room::getRoomType, roomType);
        }
        if (CollUtil.isNotEmpty(roomTypes)) {
            roomLambdaQueryChainWrapper.in(Room::getRoomType, roomTypes);
        }
        if (CollUtil.isNotEmpty(ids)) {
            roomLambdaQueryChainWrapper.in(Room::getId, ids);
        }
        if (CharSequenceUtil.isNotBlank(name)) {
            roomLambdaQueryChainWrapper.like(Room::getName, name);
        }
        if (CharSequenceUtil.isNotBlank(queryRoomParams.getCode())) {
            roomLambdaQueryChainWrapper.eq(Room::getCode, queryRoomParams.getCode());
        }
        if (Objects.nonNull(pageParams)) {
            ParamsAssert.notNull(pageParams.getPageSize());
            ParamsAssert.gt(pageParams.getPageSize(), 0);
            ParamsAssert.notNull(pageParams.getPageNum());
            ParamsAssert.gt(pageParams.getPageNum(), 0);
            roomLambdaQueryChainWrapper.page(pageParams.index(), pageParams.size());
        }
        return roomLambdaQueryChainWrapper.listWithTotal();
    }
}
