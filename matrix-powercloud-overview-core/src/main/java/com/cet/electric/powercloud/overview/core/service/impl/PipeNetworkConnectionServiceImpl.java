package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.baseconfig.common.dao.PipeNetworkConnectionModelMapper;
import com.cet.electric.baseconfig.common.entity.PipeNetworkConnectionModel;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.enums.common.FlowTypeEnum;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.model.dto.ParamsAssert;
import com.cet.electric.powercloud.overview.core.service.PipeNetworkConnectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class PipeNetworkConnectionServiceImpl implements PipeNetworkConnectionService {
    @Autowired
    private PipeNetworkConnectionModelMapper pipeNetworkConnectionModelMapper;

    @Override
    public List<PipeNetworkConnectionModel> queryPipeNetworkConnectBatch(List<ModelInfo> deviceModels, Integer flowType) {
        ParamsAssert.notNull(flowType);
        if (CollUtil.isEmpty(deviceModels)) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<PipeNetworkConnectionModel> queryChainWrapper = new LambdaQueryChainWrapper<>(pipeNetworkConnectionModelMapper);
        switch (FlowTypeEnum.getFlowTypeEnumById(flowType)) {
            case INFLOW:
                for (ModelInfo deviceModel : deviceModels) {
                    queryChainWrapper
                            .nested(n -> n
                                    .eq(PipeNetworkConnectionModel::getOutFlowId, deviceModel.getModelId())
                                    .eq(PipeNetworkConnectionModel::getOutFlowLabel, deviceModel.getModelLabel())
                            ).or();
                }
                break;
            case OUTFLOW:
                for (ModelInfo deviceModel : deviceModels) {
                    queryChainWrapper
                            .nested(n -> n
                                    .eq(PipeNetworkConnectionModel::getInflowId, deviceModel.getModelId())
                                    .eq(PipeNetworkConnectionModel::getInflowLabel, deviceModel.getModelLabel())
                            ).or();
                }
                break;
            default:
                break;
        }
        return queryChainWrapper.list();
    }
}
