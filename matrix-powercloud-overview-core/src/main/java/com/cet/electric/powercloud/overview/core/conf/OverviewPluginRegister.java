package com.cet.electric.powercloud.overview.core.conf;

import com.cet.electric.fusion.matrix.v2.client.register.JarPluginRegister;
import com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo;
import org.springframework.stereotype.Component;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/6/7 09:46
 * @description:
 */
@Component
public class OverviewPluginRegister extends JarPluginRegister {

    @Override
    public PluginRuntimeInfo getPluginRuntimeInfo() {
        PluginRuntimeInfo pluginRuntimeInfo = new PluginRuntimeInfo();
        pluginRuntimeInfo.setPluginUrlPrefex("/powercloud/overview/**");
        pluginRuntimeInfo.setPluginname("powercloud-overview");
        pluginRuntimeInfo.setProductname("powercloud");
        return pluginRuntimeInfo;
    }
}
