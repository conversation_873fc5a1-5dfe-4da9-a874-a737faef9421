package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.common.model.response.LowCodeTreeMetaData;

import java.util.List;

/**
 * 低代码全局检测
 *
 * <AUTHOR>
 * @date 2025/6/18
 */
public interface OverviewLowCodeService {
    /**
     * @param query 查询参数
     * @return 返回平台概况信息
     */
    List<PlatformOverviewLowCodeResponse> queryPlatformOverview(OverviewLowCodeParam query);

    /**
     * @param projectId 项目id
     * @return 返回用电分析信息
     */
    List<ElectricAnalysisResponse> queryElectricAnalysis(Long projectId);
    /**
     * 获取树元数据
     * @return
     */
    List<LowCodeTreeMetaData> getTreeMetaData();

    /**
     * 查询事件匹配详情
     * @param query
     * @return
     */
    List<EventMatchDetailVO> queryEventMatchDetail(OverviewLowCodeParam query, Integer status);


    List<EventStatusDistributeVO> queryEventStatusDistribute(OverviewLowCodeParam query);

    List<EventDistributeParam> queryTimeliness(OverviewLowCodeParam query);
}
