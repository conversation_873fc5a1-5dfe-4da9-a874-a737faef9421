package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.common.model.request.QueryTheSameTermRequest;
import com.cet.electric.powercloud.overview.common.model.response.PvOverviewRoomInfoResponse;

import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
public interface OverviewService {
    /**
     * @param query 地图查询参数
     * @return 返回地图信息
     */
    List<OverviewMapParam> queryOverviewMap(OverviewParam query);

    /**
     * @param query 查询参数
     * @return 返回平台概况信息
     */
    PlatformOverviewResponse queryPlatformOverview(OverviewParam query);

    /**
     * @param projectId 项目id
     * @return 返回用电分析信息
     */
    ElectricAnalysisResponse queryElectricAnalysis(Long projectId);

    /**
     * @param projectId  项目id
     * @param energyType 能源类型
     * @param startTime
     * @param endTime
     * @return 返回本月能耗趋势
     */
    List<DatalogValue> queryMonthUsageCurve(Long projectId,
                                            Integer energyType,
                                            Long startTime,
                                            Long endTime);

    /**
     * @param query 配电房详细信息查询参数
     * @return 返回配电房详细信息
     */
    OverviewMapDetailParam queryOverviewRoomDetail(OverviewParam query);

    /**
     * 站点点击详情接口
     * @param request
     * @return
     */
    PvOverviewRoomInfoResponse overviewRoomInfo(PvOverviewRoomInfo request);

    List<PeakVallyParams> queryMonthPeakVallyDistribute(Long modelId,
                                                        Long startTime,
                                                        Long endTime);

    List<QueryItemizeDataDTO> queryTheSameTerm(QueryTheSameTermRequest request);

    List<EnergyTypeDTO> queryEnergyTypes(Long projectId);

    List<EventDistributeParam> queryEventTypeDistribute(String modelLabel,
                                                        Long modelId,
                                                        Long startTime,
                                                        Long endTime,
                                                        Integer aggregationCycle);

    EventTimelinessDTO queryTimeliness(String modelLabel, Long modelId, Long startTime, Long endTime);
}
