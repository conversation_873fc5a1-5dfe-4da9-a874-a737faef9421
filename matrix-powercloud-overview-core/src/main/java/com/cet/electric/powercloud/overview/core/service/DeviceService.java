package com.cet.electric.powercloud.overview.core.service;
import com.cet.electric.baseconfig.sdk.entity.Tree;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.common.model.device.HierarchyNodeTreeVo;

import java.util.Collections;
import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/22
 */
public interface DeviceService {
    /**
     * 根据层级查询设备，不会忽略掉开关柜和储能集装箱 （4.0增加了房间粒度的权限校验）
     *
     * @param modelLabel 层级label
     * @param modelIds   层级id
     * @param isAuth     是否开启房间粒度的权限过滤
     * @return 设备
     */
    List<EquipmentAccount> queryDeviceByHierarchy(String modelLabel, List<Long> modelIds, Boolean isAuth);

    List<Tree> underLabelRooms(String modelLabel, List<Long> modelIds,  List<Integer> roomTypes);
}
