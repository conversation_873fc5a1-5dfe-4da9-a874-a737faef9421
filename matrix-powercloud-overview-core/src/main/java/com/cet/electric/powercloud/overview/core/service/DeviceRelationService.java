package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.baseconfig.common.entity.MeasuredBy;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 设备关联
 * @date 2022/10/10 10:27
 */
public interface DeviceRelationService {



    /**
     * 批量查询设备关联的核心平台设备
     *
     * @param devices 设备
     * @return 关联信息
     */
    List<MeasuredBy> queryDeviceRelationBatch(List<ModelInfo> devices);


}
