package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.powercloud.common.model.device.Project;
import com.cet.electric.powercloud.overview.common.model.dto.QueryProjectParams;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
public interface ProjectService {
    /**
     * 通用查询项目
     *
     * @param queryProjectParams 通用查询项目参数
     * @return 项目分页对象
     */
    ListWithTotal<Project> queryProject(QueryProjectParams queryProjectParams);
}
