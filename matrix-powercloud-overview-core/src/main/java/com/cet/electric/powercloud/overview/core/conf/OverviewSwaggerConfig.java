package com.cet.electric.powercloud.overview.core.conf;

import com.cet.electric.powercloud.common.constant.Constants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: mayan
 * @date: 2021/7/29 17:40
 * @description:
 */
@Configuration
@EnableSwagger2
public class OverviewSwaggerConfig {

    @Bean
    public Docket createOverviewDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("全局监测大屏")
                .apiInfo(apiInfo())
                .enable(true)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.cet.electric.powercloud.overview.core.api"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(defaultHeader());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("全局检测大屏接口")
                .description("全局检测大屏接口")
                .version("1.0")
                .build();
    }

    private static List<Parameter> defaultHeader() {
        ParameterBuilder userId = new ParameterBuilder();
        userId.name(Constants.HEADER_USER_ID).description(Constants.HEADER_USER_ID)
                .modelRef(new ModelRef("long"))
                .parameterType("header")
                .required(false)
                .build();
        ParameterBuilder tenantId = new ParameterBuilder();
        tenantId.name(Constants.TEANT_ID).description(Constants.TEANT_ID)
                .modelRef(new ModelRef("long"))
                .parameterType("header")
                .required(false)
                .build();
        List<Parameter> pars = new ArrayList<>();
        pars.add(userId.build());
        pars.add(tenantId.build());
        return pars;
    }
}
