package com.cet.electric.powercloud.overview.core.conf;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;

import javax.validation.constraints.NotNull;


/**
 * @author: ma<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/17 09:47
 * @description:
 */
public class OverviewBeanNameGenerator implements BeanNameGenerator {

    @Override
    public @NotNull String generateBeanName(@NotNull BeanDefinition beanDefinition, @NotNull BeanDefinitionRegistry beanDefinitionRegistry) {
        return "powercloudoverview_" + BeanDefinitionReaderUtils.generateBeanName(beanDefinition, beanDefinitionRegistry);
    }
}
