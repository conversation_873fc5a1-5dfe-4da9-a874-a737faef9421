package com.cet.electric.powercloud.overview.core.api;

import com.cet.electric.baseconfig.sdk.entity.Tree;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.cet.electric.powercloud.common.model.Result;
import com.cet.electric.powercloud.common.model.device.QueryCommonTreeRequest;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.common.model.request.QueryTheSameTermRequest;
import com.cet.electric.powercloud.overview.common.model.response.LowCodeTreeMetaData;
import com.cet.electric.powercloud.overview.core.service.NodeTreeService;
import com.cet.electric.powercloud.overview.core.service.OverviewLowCodeService;
import com.cet.electric.powercloud.overview.core.service.OverviewService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 全局监测低代码专用接口
 *
 * <AUTHOR>
 * @date 2025/6/18
 */
@RestController
@RequestMapping(value = "/powercloud/overview/api/lowCode/dashboard")
public class OverviewLowCodeApi {
    @Autowired
    OverviewService overviewService;
    @Autowired
    OverviewLowCodeService overviewLowCodeService;
    @Autowired
    private NodeTreeService nodeTreeService;

    @ApiOperation(value = "通用节点树-增强版，优先使用这个接口")
    @PostMapping(value = "/tree/common")
    public Result<List<Tree>> commonNodeTree(@RequestBody QueryCommonTreeRequest queryCommonNodeTreeRequest) {
        List<Tree> trees = nodeTreeService.commonNodeTreeOverview(queryCommonNodeTreeRequest);
        Tree treeNode = new Tree();
        treeNode.setModelLabel(TableNameConstant.PROJECT);
        treeNode.setName("全部");
        trees.add(0, treeNode);
        return Result.success(trees);
    }


    @ApiOperation(value = "平台概览和事件概览")
    @PostMapping(value = "/queryPlatformOverview")
    public Result<List<PlatformOverviewLowCodeResponse>> queryPlatformOverview(@RequestBody OverviewLowCodeParam query) {
        return Result.success(overviewLowCodeService.queryPlatformOverview(query));
    }

    @ApiOperation(value = "用电分析")
    @PostMapping(value = "/queryElectricAnalysis")
    public Result<List<ElectricAnalysisResponse>> queryElectricAnalysis(
            @RequestBody ModelInfo modelInfo) {
        return Result.success(overviewLowCodeService.queryElectricAnalysis(modelInfo.getModelId()));
    }

    @ApiOperation("事件处理状态统计")
    @PostMapping(value = "/queryEventStatusDistribute")
    public Result<List<EventStatusDistributeVO>> queryEventStatusDistribute(@RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewLowCodeService.queryEventStatusDistribute(request));
    }

    @ApiOperation(value = "事件列表查询")
    @PostMapping(value = "/queryEventList")
    public Result<List<EventMatchDetailVO>> queryEventList(@RequestParam Integer status, @RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewLowCodeService.queryEventMatchDetail(request, status));
    }

    @ApiOperation(value = "查询事件类型分布 只返回top5")
    @PostMapping(value = "/queryEventTypeDistribute")
    public Result<List<EventDistributeParam>> queryEventTypeDistribute(@RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewService.queryEventTypeDistribute(request.getModelLabel(),
                request.getModelId(),
                request.getStartTime(),
                request.getEndTime(),
                request.getAggregationCycle()));
    }

    @ApiOperation(value = "尖峰平谷用电量分布")
    @PostMapping("queryMonthPeakVallyDistribute")
    public Result<List<PeakVallyParams>> queryMonthPeakVallyDistribute(@RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewService.queryMonthPeakVallyDistribute(
                request.getModelId(),
                request.getStartTime(),
                request.getEndTime()));
    }

    @ApiOperation(value = "查询能源类型")
    @PostMapping(value = "/queryEnergyTypes")
    public Result<List<EnergyTypeDTO>> queryEnergyTypes(@RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewService.queryEnergyTypes(request.getModelId()));
    }

    @ApiOperation(value = "本月能耗趋势")
    @PostMapping(value = "/queryMonthUsageCurve")
    public Result<List<DatalogValue>> queryMonthUsageCurve(@RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewService.queryMonthUsageCurve(request.getModelId(),
                request.getEnergyType(),
                request.getStartTime(),
                request.getEndTime()));
    }

    @ApiOperation(value = "查询分项用能环比接口")
    @PostMapping(value = "/queryTheSameTerm")
    public Result<List<QueryItemizeDataDTO>> queryTheSameTerm(@RequestBody QueryTheSameTermRequest request) {
        return Result.success(overviewService.queryTheSameTerm(request));
    }

    @ApiOperation(value = "事件处理及时性")
    @PostMapping(value = "/queryTimeliness")
    public Result<List<EventTimelinessDTO>> queryTimeliness(@RequestBody OverviewLowCodeParam request) {
        return Result.success(Collections.singletonList(overviewService.queryTimeliness(request.getModelLabel(),
                request.getModelId(),
                request.getStartTime(),
                request.getEndTime())));
    }

    @ApiOperation(value = "事件处理及时性")
    @PostMapping(value = "/queryTimeliness/statistic")
    public Result<List<EventDistributeParam>> queryTimelinessList(@RequestBody OverviewLowCodeParam request) {
        return Result.success(overviewLowCodeService.queryTimeliness(request));
    }

    @ApiOperation(value = "获取树元数据")
    @PostMapping(value = "/getTreeMetaData")
    public Result<List<LowCodeTreeMetaData>> getTreeMetaData() {
        return Result.success(overviewLowCodeService.getTreeMetaData());
    }
}
