package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cet.electric.baseconfig.common.dao.QuantityObjectMapMapper;
import com.cet.electric.baseconfig.common.dao.QuantityObjectMapper;
import com.cet.electric.baseconfig.common.entity.QuantityObject;
import com.cet.electric.baseconfig.common.entity.QuantityObjectMap;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.overview.core.service.QuantityObjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class QuantityObjectServiceImpl implements QuantityObjectService {

    @Autowired
    private QuantityObjectMapMapper quantityObjectMapMapper;

    @Autowired
    private QuantityObjectMapper quantityObjectMapper;

    @Override
    public List<QuantityObjectMap> getQuantityObjectMapByDataId(String modelLabel, Long modelId, List<Long> dataIdList) {
        List<QuantityObject> quantityObjects = queryQuantityObjectList(modelLabel, modelId, Collections.emptyList());
        if (CollUtil.isEmpty(quantityObjects)) {
            return Collections.emptyList();
        }
        List<Long> quantityObjectIdList = quantityObjects
                .stream().map(QuantityObject::getId)
                .collect(Collectors.toList());
        return new LambdaQueryChainWrapper<>(quantityObjectMapMapper)
                .in(QuantityObjectMap::getQuantityObjectId, quantityObjectIdList)
                .in(QuantityObjectMap::getDataId, dataIdList)
                .list();
    }

    public List<QuantityObject> queryQuantityObjectList(String modelLabel, Long modelId, List<String> templateNameList) {
        LambdaQueryChainWrapper<QuantityObject> wrapper = new LambdaQueryChainWrapper<>(quantityObjectMapper);
        if (CharSequenceUtil.isNotBlank(modelLabel)) {
            wrapper.eq(QuantityObject::getMonitoredLabel, modelLabel);
        }
        if (Objects.nonNull(modelId)) {
            wrapper.eq(QuantityObject::getMonitoredId, modelId);
        }
        if (CollUtil.isNotEmpty(templateNameList)) {
            wrapper.in(QuantityObject::getName, templateNameList);
        }
        List<QuantityObject> queryList = wrapper.list();
        if (CollUtil.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        return queryList;
    }
}
