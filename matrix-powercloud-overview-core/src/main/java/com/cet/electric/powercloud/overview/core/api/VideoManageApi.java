package com.cet.electric.powercloud.overview.core.api;

import com.cet.electric.powercloud.common.client.model.CamerasDTO;
import com.cet.electric.powercloud.common.model.PageParams;
import com.cet.electric.powercloud.common.model.Result;
import com.cet.electric.powercloud.common.utils.ConvertUtil;
import com.cet.electric.powercloud.overview.common.model.request.QueryRoomVideoRequest;
import com.cet.electric.powercloud.overview.common.model.response.QueryVideoResponse;
import com.cet.electric.powercloud.overview.core.service.VideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Api(value = "/powercloud/overview/api/environment/video", tags = "视频监控接口")
@RestController
@RequestMapping(value = "/powercloud/overview/api/environment/video")
public class VideoManageApi {

    @Autowired
    private VideoService videoService;

    @ApiOperation(value = "摄像头云台控制")
    @GetMapping(value = "/controlsCamera")
    public Result<String> controlsCamera(@RequestParam Long id, @RequestParam @ApiParam("上0，下1，左2，右3，左上4，左下5，右上6，右下7，放大8，缩小9") Integer controlType) {
        videoService.controlsCamera(id,controlType);
        return Result.success();
    }

    @ApiOperation(value = "获取摄像头播放地址")
    @GetMapping(value = "/cameraUrl")
    public Result<String> getCameraUrl(@RequestParam Long id) {
        return Result.success(videoService.getCameraUrl(id));
    }

    @ApiOperation(value = "获取配电室关联的摄像头列表")
    @PostMapping(value = "/queryVideoByRoom")
    public Result<List<QueryVideoResponse>> queryVideoByRoom(@Validated @RequestBody QueryRoomVideoRequest request) {
        List<CamerasDTO> cameras = videoService.queryRoomCameras(request.getRoomId(), request.getClarity());
        if (Objects.nonNull(request.getPageParams())) {
            return Result.success(toResponse(PageParams.page(request.getPageParams(), cameras)), cameras.size());
        }
        return Result.success(toResponse(cameras));
    }

    private List<QueryVideoResponse> toResponse(List<CamerasDTO> cameras) {
        List<QueryVideoResponse> list = new ArrayList<>(cameras.size());
        for (CamerasDTO camerasDTO : cameras) {
            QueryVideoResponse response = new QueryVideoResponse();
            ConvertUtil.copyProperties(camerasDTO, response);
            list.add(response);
        }
        list.sort(Comparator.comparing(QueryVideoResponse::getId));
        return list;
    }
}
