package com.cet.electric.powercloud.overview.core.service.impl;

import com.cet.electric.commons.ApiResult;
import com.cet.electric.powercloud.overview.core.service.WisdomService;
import com.cet.powercloud.wisdom.feign.spring.boot.api.WisdomCameraRestApi;
import com.cet.powercloud.wisdom.feign.spring.boot.model.QueryCameraParam;
import com.cet.powercloud.wisdom.feign.spring.boot.model.WisdomCamera;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Copyright © 1993-2025 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2025-03-17  16:07
 * @description:
 */
@Service
@Slf4j
public class WisdomServiceImpl implements WisdomService {

    @Resource
    WisdomCameraRestApi wisdomCameraRestApi;

    @Override
    public ApiResult<List<WisdomCamera>> queryCamera(QueryCameraParam param) {
        ApiResult<List<WisdomCamera>> apiResult;
        // 服务不可达，直接返回空数据，不是抛出异常，经过停掉服务的试验，服务不可达抛出的是RetryableException
        try {
            apiResult = wisdomCameraRestApi.queryCamera(param);
        } catch (RetryableException e) {
            apiResult = ApiResult.newResult(0, "", Collections.emptyList());
        }
        return apiResult;
    }
}
