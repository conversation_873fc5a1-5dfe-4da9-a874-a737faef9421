package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.commons.ApiResult;
import com.cet.powercloud.wisdom.feign.spring.boot.model.QueryCameraParam;
import com.cet.powercloud.wisdom.feign.spring.boot.model.WisdomCamera;

import java.util.List;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2023-12-06  11:08
 * @description:
 */
public interface WisdomService {

    ApiResult<List<WisdomCamera>> queryCamera(QueryCameraParam param);
}
