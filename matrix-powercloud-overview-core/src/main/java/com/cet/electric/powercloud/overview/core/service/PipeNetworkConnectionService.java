package com.cet.electric.powercloud.overview.core.service;

import com.cet.electric.baseconfig.common.entity.PipeNetworkConnectionModel;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;

import java.util.List;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/22
*/public interface PipeNetworkConnectionService {
    /**
     * 批量查询管网连接关系
     *
     * @param deviceModels 设备model
     * @param flowType     查询上端还是下端 1上端 2下端
     * @return 管网连接关系
     */
    List<PipeNetworkConnectionModel> queryPipeNetworkConnectBatch(List<ModelInfo> deviceModels, Integer flowType);
}
