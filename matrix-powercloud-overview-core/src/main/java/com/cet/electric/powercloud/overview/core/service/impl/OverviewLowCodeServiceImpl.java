package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.cet.electric.baseconfig.common.dao.DeviceTypeMapper;
import com.cet.electric.baseconfig.common.entity.DeviceType;
import com.cet.electric.baseconfig.common.entity.MeasuredBy;
import com.cet.electric.baseconfig.sdk.entity.param.LabelAndId;
import com.cet.electric.baseconfig.sdk.service.NodeRelationService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.modelservice.common.query.ModelQuery;
import com.cet.electric.modelservice.core.query.impl.ModelExtendService;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper;
import com.cet.electric.powercloud.common.client.DeviceDataExtendService;
import com.cet.electric.powercloud.common.client.model.SystemEvent;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.enums.common.SystemEventTypeEnum;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.common.model.device.Project;
import com.cet.electric.powercloud.common.model.device.Room;
import com.cet.electric.powercloud.common.utils.AuthUtil;
import com.cet.electric.powercloud.overview.common.constants.OverviewConstant;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.ModelInfo;
import com.cet.electric.powercloud.overview.common.enums.ConfirmEventStatusEnum;
import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.common.model.response.LowCodeModelMetaData;
import com.cet.electric.powercloud.overview.common.model.response.LowCodeTreeMetaData;
import com.cet.electric.powercloud.overview.common.utils.DoubleUtil;
import com.cet.electric.powercloud.overview.core.mapper.SystemEventMapper;
import com.cet.electric.powercloud.overview.core.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/6/18
 */
@Service
@Slf4j
public class OverviewLowCodeServiceImpl implements OverviewLowCodeService {
    @Autowired
    OverviewService overviewService;
    @Autowired
    NodeRelationService nodeRelationService;
    @Autowired
    ProjectService projectService;
    @Autowired
    BusinessAuthService businessAuthService;
    @Autowired
    RoomService roomService;
    @Autowired
    NodeTreeService nodeTreeService;
    @Autowired
    SystemEventMapper systemEventMapper;
    @Autowired
    DeviceRelationService deviceRelationService;
    @Autowired
    DeviceTypeMapper deviceTypeMapper;
    @Autowired
    DeviceDataExtendService deviceDataExtendService;
    @Autowired
    ModelExtendService modelExtendService;

    @Override
    public List<PlatformOverviewLowCodeResponse> queryPlatformOverview(OverviewLowCodeParam query) {
        PlatformOverviewLowCodeResponse platformResponse = new PlatformOverviewLowCodeResponse();
        List<Long> authProjectIds;
        if (Objects.isNull(query.getModelId())) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(query.getModelId());
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return Collections.singletonList(platformResponse);
        }
        // 项目总数
        platformResponse.setProjectCount(authProjectIds.size());
        QueryProjectParams queryProjectParams = new QueryProjectParams();
        queryProjectParams.setIds(authProjectIds);
        List<Project> authProjects = projectService.queryProject(queryProjectParams).getList();
        setSafeRunDays(authProjects, platformResponse);
        // 查询多个项目下的权限房间
        List<Room> roomList = roomService.queryAuthRoomByProjectIds(authProjectIds, TableNameConstant.PROJECT);
        if (CollUtil.isEmpty(roomList)) {
            return Collections.singletonList(platformResponse);
        }
        platformResponse.setRoomCount(roomList.size());
        List<Long> authRoomIds = roomList.stream().map(Room::getId).collect(Collectors.toList());
        // 查询项目下设备
        List<EquipmentAccount> equipmentAccountList = nodeTreeService.getEquipmentAccounts(TableNameConstant.ROOM, authRoomIds);
        if (CollUtil.isEmpty(equipmentAccountList)) {
            return Collections.singletonList(platformResponse);
        }
        // 业务事件查询
        setProcessingState(query, platformResponse, equipmentAccountList);

        List<ModelInfo> modelInfoList = equipmentAccountList.stream()
                .map(self -> new ModelInfo(self.getId(), self.getModelLabel())).collect(Collectors.toList());
        List<MeasuredBy> measuredByList = deviceRelationService.queryDeviceRelationBatch(modelInfoList);
        if (CollUtil.isEmpty(measuredByList)) {
            return Collections.singletonList(platformResponse);
        }
        // 设备在线率
        Map<String, List<Long>> labelToIdsMap = equipmentAccountList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getModelLabel,
                        Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        queryDeviceStatus(query, measuredByList, platformResponse, labelToIdsMap);

        return Collections.singletonList(platformResponse);
    }

    @Override
    public List<ElectricAnalysisResponse> queryElectricAnalysis(Long projectId) {
        ElectricAnalysisResponse electricAnalysisResponse = overviewService.queryElectricAnalysis(projectId);
        // 计算日环比和月环比，任一数据为null则不计算
        Double today = electricAnalysisResponse.getTodayEnergyUsage();
        Double yesterday = electricAnalysisResponse.getYesterdayEnergyUsage();
        Double month = electricAnalysisResponse.getMonthEnergyUsage();
        Double lastMonth = electricAnalysisResponse.getLastMonthEnergyUsage();
        Double dayPeriodOverPeriodChange = null;
        Double monthPeriodOverPeriodChange = null;
        if (today != null && yesterday != null && month != null && lastMonth != null) {
            if (yesterday == 0) {
                dayPeriodOverPeriodChange = 1.0;
            } else {
                dayPeriodOverPeriodChange = (today - yesterday) / yesterday;
            }
            if (lastMonth == 0) {
                monthPeriodOverPeriodChange = 1.0;
            } else {
                monthPeriodOverPeriodChange = (month - lastMonth) / lastMonth;
            }
        }
        electricAnalysisResponse.setDayPeriodOverPeriodChange(dayPeriodOverPeriodChange);
        electricAnalysisResponse.setMonthPeriodOverPeriodChange(monthPeriodOverPeriodChange);
        return Arrays.asList(electricAnalysisResponse);
    }

    private void queryDeviceStatus(OverviewLowCodeParam query, List<MeasuredBy> measuredByList, PlatformOverviewLowCodeResponse platform,
                                   Map<String, List<Long>> labelToIdsMap) {
        List<Integer> pecDeviceIds = measuredByList.stream()
                .map(MeasuredBy::getMeasuredBy)
                .distinct()
                .collect(Collectors.toList());
        //获取设备在线情况
        Map<Integer, Integer> onlineMap = deviceDataExtendService.queryDeviceStatus(pecDeviceIds);
        //获取设备报警情况
        List<DeviceAlarmGroupCount> deviceAlarmGroupCountList = calcDeviceAlarmGroupCount(labelToIdsMap, query);
        Map<ModelInfo, DeviceAlarmGroupCount> deviceAlarmGroupCountMap = deviceAlarmGroupCountList.stream()
                .filter(self -> Objects.nonNull(self.getCount()) && self.getCount() > 0)
                .collect(Collectors.toMap(item -> new ModelInfo(item.getDeviceId(), item.getDeviceLabel()), self -> self));
        // 查询出可关联采集设备的设备label
        List<DeviceType> deviceTypes = new LambdaQueryChainWrapper<>(deviceTypeMapper)
                .eq(DeviceType::getEnable, Boolean.TRUE)
                .eq(DeviceType::getAssociativePecDevice, Boolean.TRUE)
                .list();
        Set<String> deviceLabels = deviceTypes.stream()
                .map(DeviceType::getDeviceLabel)
                .collect(Collectors.toSet());
        int onlineCount = 0;
        int alarmDeviceCount = 0;
        Map<ModelInfo, Integer> deviceInfoToPecMap = measuredByList.stream()
                .collect(Collectors.toMap(item -> new ModelInfo(item.getMonitoredId(), item.getMonitoredLabel()),
                        MeasuredBy::getMeasuredBy, (v1, v2) -> v1));
        List<ModelInfo> modelInfos = new ArrayList<>();
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            for (Long deviceId : entry.getValue()) {
                ModelInfo modelInfo = new ModelInfo(deviceId, entry.getKey());
                modelInfos.add(modelInfo);
                Integer status = onlineMap.get(deviceInfoToPecMap.get(modelInfo));
                // 此管网设备需要支持关联采集设备，才进行在线状态的统计
                if (deviceLabels.contains(modelInfo.getModelLabel()) && Objects.nonNull(status) && (Objects.equals(0, status) || Objects.equals(1, status))) {
                    onlineCount++;
                }
                DeviceAlarmGroupCount deviceAlarmGroupCount = deviceAlarmGroupCountMap.get(modelInfo);
                if (Objects.nonNull(deviceAlarmGroupCount)) {
                    alarmDeviceCount++;
                }
            }
        }
        platform.setAlarmDeviceCount(alarmDeviceCount);
        platform.setDeviceCount(onlineCount);
        // 分母是支持关联采集设备并且关联了采集设备的管网设备的数量
        Set<LabelAndId> deviceModels = measuredByList.stream()
                .filter(it -> deviceLabels.contains(it.getMonitoredLabel()))
                .map(it -> new LabelAndId(it.getMonitoredLabel(), it.getMonitoredId()))
                .collect(Collectors.toSet());
        platform.setDeviceOnlineRate(DoubleUtil.divide((double) onlineCount, (double) deviceModels.size()));
        platform.setAlarmDevicePercent(DoubleUtil.divide((double) alarmDeviceCount, (double) modelInfos.size()));
        // 监测终端数量
        platform.setMeterCount(deviceModels.size());
    }

    private List<DeviceAlarmGroupCount> calcDeviceAlarmGroupCount(Map<String, List<Long>> labelToIdsMap, OverviewLowCodeParam query) {
        LambdaQueryWrapper<SystemEvent> wrapper = new LambdaQueryWrapper<>(SystemEvent.class)
                .groupBy(SystemEvent::getObjectId, SystemEvent::getObjectLabel);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            wrapper.nested(n -> n.between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                    .eq(SystemEvent::getConfirmEventStatus, ConfirmEventStatusEnum.UNCONFIRMED.getCode())
                    .in(SystemEvent::getObjectId, entry.getValue())
                    .eq(SystemEvent::getObjectLabel, entry.getKey())).or();
        }
        ModelQuery modelQuery = wrapper.build();
        ApiResult<List<DeviceAlarmGroupCount>> eventCountResult = modelExtendService.queryModelData(modelQuery, DeviceAlarmGroupCount.class);
        if (!eventCountResult.isSuccess()) {
            log.error("分组查询设备报警信息异常,{}", eventCountResult.getMsg());
            return Collections.emptyList();
        }
        return eventCountResult.getData();
    }

    private void setProcessingState(OverviewLowCodeParam query,
                                    PlatformOverviewLowCodeResponse platformResponse,
                                    List<EquipmentAccount> equipmentAccountList) {

        Map<String, List<Long>> labelToIdsMap = equipmentAccountList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getModelLabel,
                        Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        // 本月告警总数
        Integer thisTotal = getSystemEventCount(query, labelToIdsMap, null);
        platformResponse.setAlarmCount(thisTotal);
    }

    /**
     * 查询事件 最新的50条
     *
     * @return ListWithTotal
     */
    private List<SystemEvent> getSystemEventList(OverviewLowCodeParam query,
                                                 Map<String, List<Long>> labelToIdsMap,
                                                 Integer confirmEventStatus) {
        LambdaQueryChainWrapper<SystemEvent> thisWrapper = new LambdaQueryChainWrapper<>(systemEventMapper)
                .orderByDesc(SystemEvent::getEventTime)
                .page(0, 50);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            thisWrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                            .between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                            .in(SystemEvent::getObjectId, entry.getValue())
                            .eq(SystemEvent::getConfirmEventStatus, confirmEventStatus))
                    .or();
        }
        return thisWrapper.list();
    }

    /**
     * 查询事件 最新的50条
     *
     * @return ListWithTotal
     */
    private Integer getSystemEventCount(OverviewLowCodeParam query,
                                        Map<String, List<Long>> labelToIdsMap,
                                        Integer confirmEventStatus) {
        LambdaQueryChainWrapper<SystemEvent> thisWrapper = new LambdaQueryChainWrapper<>(systemEventMapper)
                .orderByDesc(SystemEvent::getEventTime)
                .page(0, 50);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            thisWrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                            .between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                            .in(SystemEvent::getObjectId, entry.getValue())
                            .eq(SystemEvent::getConfirmEventStatus, confirmEventStatus))
                    .or();
        }
        return thisWrapper.count();
    }

    private void setSafeRunDays(List<Project> authProjects, PlatformOverviewLowCodeResponse platformResponse) {
        authProjects.stream()
                .filter(project -> Objects.nonNull(project.getCommissionDate()))
                .map(project -> System.currentTimeMillis() - project.getCommissionDate())
                .max(Comparator.comparing(Long::valueOf))
                .ifPresent(maxTimeDelta -> platformResponse.setSafeRunDays(maxTimeDelta / OverviewConstant.DAY_INTERVAL));
    }

    private List<Long> getAuthProjectIds() {
        return businessAuthService.getAuthNodeIds(AuthModelNodesEnum.PROJECT, AuthUtil.getUserId(), AuthUtil.getTenantId());
    }

    @Override
    public List<LowCodeTreeMetaData> getTreeMetaData() {
        LowCodeTreeMetaData lowCodeTreeMetaData = new LowCodeTreeMetaData();
        lowCodeTreeMetaData.setAlias("项目");
        lowCodeTreeMetaData.setLevel(1);
        lowCodeTreeMetaData.setModelList(Collections.singletonList(new LowCodeModelMetaData("项目", "project")));
        return Arrays.asList(lowCodeTreeMetaData);
    }

    @Override
    public List<EventMatchDetailVO> queryEventMatchDetail(OverviewLowCodeParam query, Integer status) {
        // 1. 获取有权限的项目ID
        List<Long> authProjectIds;
        if (Objects.isNull(query.getModelId())) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(query.getModelId());
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return Collections.emptyList();
        }
        // 2. 查项目下有权限的房间
        List<Room> roomList = roomService.queryAuthRoomByProjectIds(authProjectIds, TableNameConstant.PROJECT);
        if (CollUtil.isEmpty(roomList)) {
            return Collections.emptyList();
        }
        List<Long> authRoomIds = roomList.stream().map(Room::getId).collect(Collectors.toList());
        // 3. 查房间下的设备
        List<EquipmentAccount> equipmentAccountList = nodeTreeService.getEquipmentAccounts(TableNameConstant.ROOM, authRoomIds);
        if (CollUtil.isEmpty(equipmentAccountList)) {
            return Collections.emptyList();
        }
        // 4. 按设备分组
        Map<String, List<Long>> labelToIdsMap = equipmentAccountList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getModelLabel,
                        Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        List<SystemEvent> systemEventList = getSystemEventList(query, labelToIdsMap, status);
        if (CollUtil.isEmpty(systemEventList)) {
            return Collections.emptyList();
        }
        // 5. 事件和设备信息映射为VO
        Map<String, EquipmentAccount> equipmentAccountMap = equipmentAccountList.stream()
                .collect(Collectors.toMap(self -> (self.getModelLabel() + self.getId()), self -> self));
        return convertToEventDetail(systemEventList, equipmentAccountMap);
    }

    @Override
    public List<EventStatusDistributeVO> queryEventStatusDistribute(OverviewLowCodeParam query) {
        List<Long> authProjectIds;
        if (Objects.isNull(query.getModelId())) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(query.getModelId());
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return Collections.emptyList();
        }
        // 项目总数
        QueryProjectParams queryProjectParams = new QueryProjectParams();
        queryProjectParams.setIds(authProjectIds);
        // 查询多个项目下的权限房间
        List<Room> roomList = roomService.queryAuthRoomByProjectIds(authProjectIds, TableNameConstant.PROJECT);
        if (CollUtil.isEmpty(roomList)) {
            return Collections.emptyList();
        }
        List<Long> authRoomIds = roomList.stream().map(Room::getId).collect(Collectors.toList());
        // 查询项目下设备
        List<EquipmentAccount> equipmentAccountList = nodeTreeService.getEquipmentAccounts(TableNameConstant.ROOM, authRoomIds);
        if (CollUtil.isEmpty(equipmentAccountList)) {
            return Collections.emptyList();
        }
        Map<String, List<Long>> labelToIdsMap = equipmentAccountList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getModelLabel,
                        Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        List<EventStatusDistributeVO> result = new LinkedList<>();
        EventStatusDistributeVO eventStatusDistributeVO = new EventStatusDistributeVO();
        // 查询本月 未处理事件 最新的50条
        Integer unhandledAlarmCount = getSystemEventCount(query, labelToIdsMap, ConfirmEventStatusEnum.UNCONFIRMED.getCode());
        eventStatusDistributeVO.setStatus(ConfirmEventStatusEnum.UNCONFIRMED.getCode());
        eventStatusDistributeVO.setStatusText(ConfirmEventStatusEnum.UNCONFIRMED.getMessage());
        eventStatusDistributeVO.setNumber(unhandledAlarmCount);
        result.add(eventStatusDistributeVO);
        eventStatusDistributeVO = new EventStatusDistributeVO();
        // 查询本月 处理中事件 最新的50条
        Integer confirmingCount = getSystemEventCount(query, labelToIdsMap, ConfirmEventStatusEnum.CONFIRMING.getCode());
        eventStatusDistributeVO.setStatus(ConfirmEventStatusEnum.CONFIRMING.getCode());
        eventStatusDistributeVO.setStatusText(ConfirmEventStatusEnum.CONFIRMING.getMessage());
        eventStatusDistributeVO.setNumber(confirmingCount);
        result.add(eventStatusDistributeVO);
        eventStatusDistributeVO = new EventStatusDistributeVO();
        // 查询本月 已处理事件 最新的50条
        Integer confirmedCount = getSystemEventCount(query, labelToIdsMap, ConfirmEventStatusEnum.CONFIRMED.getCode());
        eventStatusDistributeVO.setStatus(ConfirmEventStatusEnum.CONFIRMED.getCode());
        eventStatusDistributeVO.setStatusText(ConfirmEventStatusEnum.CONFIRMED.getMessage());
        eventStatusDistributeVO.setNumber(confirmedCount);
        result.add(eventStatusDistributeVO);
        return result;
    }

    @Override
    public List<EventDistributeParam> queryTimeliness(OverviewLowCodeParam query) {
        EventTimelinessDTO eventTimelinessDTO = overviewService.queryTimeliness(query.getModelLabel(),
                query.getModelId(),
                query.getStartTime(),
                query.getEndTime());
        List<EventDistributeParam> result = new LinkedList<>();
        EventDistributeParam eventDistributeParam = new EventDistributeParam();
        eventDistributeParam.setName("一小时内处理");
        eventDistributeParam.setNum(eventTimelinessDTO.getOneHourProcess());
        result.add(eventDistributeParam);
        eventDistributeParam = new EventDistributeParam();
        eventDistributeParam.setName("一天内处理");
        eventDistributeParam.setNum(eventTimelinessDTO.getOneDayProcess());
        result.add(eventDistributeParam);
        eventDistributeParam = new EventDistributeParam();
        eventDistributeParam.setName("两天内处理");
        eventDistributeParam.setNum(eventTimelinessDTO.getTwoDayProcess());
        result.add(eventDistributeParam);
        eventDistributeParam = new EventDistributeParam();
        eventDistributeParam.setName("一周内处理");
        eventDistributeParam.setNum(eventTimelinessDTO.getOneWeekProcess());
        result.add(eventDistributeParam);
        return result;
    }

    private List<EventMatchDetailVO> convertToEventDetail(List<SystemEvent> events,
                                                          Map<String, EquipmentAccount> equipmentAccountMap) {
        return events.stream().map(item -> {
            EventMatchDetailVO detailVO = new EventMatchDetailVO();
            detailVO.setStartEventLevel(item.getLevel());
            detailVO.setStartEventLevelText(item.getName());
            detailVO.setEventType(item.getEventType());
            detailVO.setEventTypeText(SystemEventTypeEnum.of(item.getEventType()));
            detailVO.setConfirmEventStatus(item.getConfirmEventStatus());
            detailVO.setConfirmEventStatusText(item.getConfirmEventStatusText());
            detailVO.setStartEventTime(item.getEventTime());
            detailVO.setStartEventDescription(item.getDescription());
            String key = item.getObjectLabel() + item.getObjectId();
            EquipmentAccount equipmentAccount = equipmentAccountMap.get(key);
            detailVO.setDeviceId(item.getObjectId());
            detailVO.setDeviceLabel(item.getObjectLabel());
            if (Objects.isNull(equipmentAccount)) {
                return detailVO;
            }
            detailVO.setBuildingId(equipmentAccount.getBuildingId());
            detailVO.setProjectId(equipmentAccount.getProjectId());
            detailVO.setRoomId(equipmentAccount.getRoomId());
            return detailVO;
        }).collect(Collectors.toList());
    }
}
