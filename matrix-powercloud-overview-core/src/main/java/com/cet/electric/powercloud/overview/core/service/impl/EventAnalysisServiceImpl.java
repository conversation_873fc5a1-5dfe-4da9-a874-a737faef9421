package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.powercloud.common.client.model.SystemEvent;
import com.cet.electric.powercloud.common.model.device.EquipmentAccount;
import com.cet.electric.powercloud.overview.common.enums.ConfirmEventStatusEnum;
import com.cet.electric.powercloud.overview.common.model.dto.EventAnalysisParams;
import com.cet.electric.powercloud.overview.common.model.dto.EventOverviewParam;
import com.cet.electric.powercloud.overview.core.mapper.SystemEventMapper;
import com.cet.electric.powercloud.overview.core.service.DeviceService;
import com.cet.electric.powercloud.overview.core.service.EventAnalysisService;
import com.cet.electric.powercloud.overview.core.service.HiddenDangerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/30
 */
@Service
public class EventAnalysisServiceImpl implements EventAnalysisService {
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private HiddenDangerService hiddenDangerService;

    @Autowired
    private SystemEventMapper systemEventMapper;

    @Override
    public EventOverviewParam queryEventMatchOverview(EventAnalysisParams query) {
        List<EquipmentAccount> equipmentList = deviceService.queryDeviceByHierarchy(query.getModelLabel(), query.getModelIds(), true);
        EventOverviewParam eventOverviewParam = new EventOverviewParam();
        if (CollUtil.isEmpty(equipmentList)) {
            return eventOverviewParam;
        }

        Map<String, List<Long>> labelToIdsMap = equipmentList.stream()
                .collect(
                        Collectors.groupingBy(EquipmentAccount::getModelLabel,
                                Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        // 补充线损等事件
        hiddenDangerService.supplementarySystemEventQueryConditions(Collections.emptyList(),
                labelToIdsMap,
                equipmentList);
        LambdaQueryChainWrapper<SystemEvent> totalWrapper = new LambdaQueryChainWrapper<>(systemEventMapper);
        LambdaQueryChainWrapper<SystemEvent> unconfirmedWrapper = new LambdaQueryChainWrapper<>(systemEventMapper);
        LambdaQueryChainWrapper<SystemEvent> confirmingWrapper = new LambdaQueryChainWrapper<>(systemEventMapper);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            totalWrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                    .between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                    .in(SystemEvent::getObjectId, entry.getValue())).or();
            unconfirmedWrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                    .between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                    .in(SystemEvent::getObjectId, entry.getValue())
                    .eq(SystemEvent::getConfirmEventStatus, ConfirmEventStatusEnum.UNCONFIRMED.getCode())).or();
            confirmingWrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                    .between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                    .in(SystemEvent::getObjectId, entry.getValue())
                    .eq(SystemEvent::getConfirmEventStatus, ConfirmEventStatusEnum.CONFIRMING.getCode())).or();
        }
        Integer total = totalWrapper.count();
        Integer totalUnconfirmed = unconfirmedWrapper.count();
        Integer totalConfirming = confirmingWrapper.count();
        if (Objects.nonNull(total)) {
            eventOverviewParam.setAlarmCount(total);
            eventOverviewParam.setUnhandledAlarmCount(totalUnconfirmed);
            eventOverviewParam.setConfirmingAlarmCount(totalConfirming);
            eventOverviewParam.setHandledAlarmCount(total - totalUnconfirmed - totalConfirming);
        }

        return eventOverviewParam;
    }
}
