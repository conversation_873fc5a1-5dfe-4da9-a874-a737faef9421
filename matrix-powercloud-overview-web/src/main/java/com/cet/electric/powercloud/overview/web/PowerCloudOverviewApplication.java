package com.cet.electric.powercloud.overview.web;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Copyright © 1993-2024 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: lhw
 * @date: 2024-10-21  15:06
 * @description:
 */
@EnableAsync
@EnableScheduling
@EnableKnife4j
@SpringBootApplication
public class PowerCloudOverviewApplication {
    public static void main(String[] args) {
        SpringApplication.run(PowerCloudOverviewApplication.class, args);
    }
}
