a.error.enum.invalid=Unexpected enum
electric.error.analysis.service.queryYearOnYearData=Only single node query is supported
electric.error.analysis.service.queryMonthOnMonthData=Only single node query is supported
electric.error.analysis.service.queryEnergyReportData=The energy consumption report supports only one node query
electric.error.analysis.service.getChildrenNode.level=Request level error parameter abnormal
electric.error.analysis.service.getChildrenNode.model=The model node does not exist
electric.error.analysis.service.getName.modelService=Query model service exception msg {0}
electric.error.analysis.service.getName.param=Parameter exception the model node does not exist
electric.error.energyEnter.service.saveDeviceRepairDataLog=The device has periodic records and cannot be manually recorded
electric.error.energyEnter.controller.table=Table data is empty
electric.error.energyEnter.controller.device=Devices are not associated
electric.error.regional.service.queryChainData=Incorrect time parameter:{0}
electric.info.analysis.service.queryEnergyReportData.subtotal=Subtotal
electric.info.itemized.service.queryStatisticalAnalysis.total=Total
electric.error.energyEnter.controller.deviceEnergy=Meter energy consumption -
electric.info.voltageType.normal=normalVoltage
electric.info.voltageType.over=overVoltage
electric.info.voltageType.low=underVoltage
electric.info.electric.type.1=Aircondition
electric.info.electric.type.2=Lighting
electric.info.electric.type.3=Motive Force
electric.info.electric.type.4=Backup
electric.info.electric.type.5=Other
electric.info.water.type.6=Tap Water
electric.info.water.type.7=Circulating Water
electric.info.water.type.8=Refrigerant Water
electric.info.water.type.9=Hot Water
electric.info.water.type.10=Other
electric.info.gas.type.11=Gas
electric.info.gas.type.12=Natural Gas
electric.info.gas.type.13=Other
electric.info.energyEnter.controller.logTime=logTime
electric.info.energyEnter.controller.data=value
system.error.tsc.existName=The scheme name is duplicate, please change the scheme name and save again!
system.error.tsc.noTSC=The time sharing scheme does not exist!
system.error.tsc.duplicateNode=There are duplicate node associations (each project, building, floor, room node can only be associated with one scheme)
a.info.com.cet.electric.powercloud.common.business.success=Request successful
a.info.com.cet.electric.powercloud.common.business.fail=Request failure
a.info.com.cet.electric.powercloud.common.business.param=HTTP parameter verification failed
a.info.com.cet.electric.powercloud.common.business.illegalParameter=Illegal parameter
system.info.energy.1=Energy
system.info.energy.2=Electric Energy
system.info.energy.3=Water
system.info.energy.4=Tap Water
system.info.energy.5=Circulating Water
system.info.energy.6=Refrigerant Water
system.info.energy.7=Hot Water
system.info.energy.8=Steam
system.info.energy.9=Oil
system.info.energy.10=Diesel Oil
system.info.energy.11=Gasoline
system.info.energy.12=Coal
system.info.energy.13=Discounted Standard Coal
system.info.energy.14=Fuel Gas
system.info.energy.15=Natural Gas
system.info.energy.16=Compressed Air
system.info.energy.17=CO
system.info.energy.18=CO2
system.info.energy.19=CH4
system.info.energy.20=Oxygen
system.info.energy.21=Liquid
system.info.energy.22=Carbon
system.info.energy.23=Reverse Active Energy
system.info.energy.24=Cold
system.info.energy.25=Positive Reactive Electric
system.info.energy.26=Network Signal
system.info.energy.27=Nitrogen
electricsafety.info.eventType.1=Over temperature
electricsafety.info.eventType.2=Over current
electricsafety.info.eventType.3=Residual current
electricsafety.info.eventType.4=Phase loss
electricsafety.info.eventType.5=Communication
electricsafety.info.eventType.6=Over voltage
electricsafety.info.eventType.7=Under voltage
electricsafety.info.eventType.8=Three-Phase unbalance
electricsafety.info.eventType.9=Probe fault
electricsafety.info.eventType.10=Switching
electricsafety.info.eventType.801=Work overtime
electricsafety.info.eventType.1000=Work order assignment
electricsafety.info.eventType.802=Hourly line loss alarm
electricsafety.info.eventType.803=Daily loss alarm
electricsafety.info.eventType.901=Shield warning
electricsafety.info.eventType.902=Wash warning