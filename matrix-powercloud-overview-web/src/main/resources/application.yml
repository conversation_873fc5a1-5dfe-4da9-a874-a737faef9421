server:
  port: 8104

spring:
  redis:
    host: ************
    password: sA123456
    port: 26379
  application:
    name: overview
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    include: overview
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

eureka:
  client:
    service-url:
      defaultZone: ***********************************/eureka/  # Eureka服务地址
  instance:
    prefer-ip-address: true # 使用IP地址注册
    ip-address: ************

cet:
  base-service:
    model-service:
      url: ${MODE_PLUS_URL:************:8085}
    device-data-service:
      url: ${DEVICE-DATA-SERVICE:************:5050}
    cloud-auth-service:
      url: ${CLOUD-AUTH-SERVICE:************:2014}
    pec-node-service:
      url: ${PEC-NODE-SERVICE:************:8180}
    notice-service:
      url: ${NOTICE-SERVICE:************:5070}
  feign:
    url:
      videoService: ${VIDEO-MANAGEMENT-SERVICE:************:8082}
  loss:
    alert: true
  powercloud:
    wisdom:
      url: ${POWERCLOUD-AI-OPS:************:8030}