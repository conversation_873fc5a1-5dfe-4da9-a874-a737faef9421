<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.electric</groupId>
        <artifactId>matterhorn-basic-service-parent</artifactId>
        <version>0.0.13</version>
        <relativePath />
    </parent>
    <artifactId>matrix-powercloud-overview-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <modules>
        <module>matrix-powercloud-overview-core</module>
        <module>matrix-powercloud-overview-web</module>
        <module>matrix-powercloud-overview-common</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <powercloud-common.version>1.0.2.60</powercloud-common.version>
        <revision>1.3.0.0</revision>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>matrix-powercloud-overview-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>matrix-powercloud-overview-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>matrix-powercloud-overview-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>matrix-powercloud-common</artifactId>
                <version>${powercloud-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>fusion-matrix-v2-parent</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>matterhorn-basic-service-parent</artifactId>
                <version>0.0.13</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 添加仓库配置来绕过镜像阻止 -->
    <repositories>
        <repository>
            <id>*************_9092</id>
            <name>Internal Maven Repository</name>
            <url>http://*************:9092/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun-central</id>
            <name>Aliyun Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>*************_9092</id>
            <name>Internal Maven Plugin Repository</name>
            <url>http://*************:9092/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>aliyun-central</id>
            <name>Aliyun Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>