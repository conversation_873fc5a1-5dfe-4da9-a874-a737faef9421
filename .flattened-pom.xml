<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.cet.electric</groupId>
    <artifactId>matterhorn-basic-service-parent</artifactId>
    <version>0.0.13</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.cet.electric</groupId>
  <artifactId>matrix-powercloud-overview-parent</artifactId>
  <version>1.3.0.0</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>matrix-powercloud-overview-core</module>
    <module>matrix-powercloud-overview-web</module>
    <module>matrix-powercloud-overview-common</module>
  </modules>
  <properties>
    <powercloud-common.version>1.0.2.60</powercloud-common.version>
    <revision>1.3.0.0</revision>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>matrix-powercloud-overview-core</artifactId>
        <version>1.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>matrix-powercloud-overview-common</artifactId>
        <version>1.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>matrix-powercloud-overview-web</artifactId>
        <version>1.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>matrix-powercloud-common</artifactId>
        <version>${powercloud-common.version}</version>
      </dependency>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>fusion-matrix-v2-parent</artifactId>
        <version>1.0.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>matterhorn-basic-service-parent</artifactId>
        <version>0.0.13</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>10.12.135.233_9092</id>
      <name>Internal Maven Plugin Repository</name>
      <url>http://10.12.135.233:9092/repository/maven-public</url>
    </pluginRepository>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-central</id>
      <name>Aliyun Central</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </pluginRepository>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Maven Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
              <pomElements>
                <parent>expand</parent>
                <distributionManagement>remove</distributionManagement>
                <repositories>remove</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <fork>false</fork>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
